-- ====================================================================
-- 数据库触发器和函数定义
-- 创建时间: 2025-01-21
-- 描述: 项目中所有触发器和相关函数的定义
-- 用途: 独立管理触发器，便于部署时单独处理
-- ====================================================================

-- --------------------------------------------------------------------
-- 1. 地址位置更新触发器函数
-- 
-- 功能：当用户地址表的经纬度字段更新时，自动更新PostGIS的location字段
-- 适用表：user_addresses
-- 触发条件：INSERT 或 UPDATE latitude, longitude 字段时
-- --------------------------------------------------------------------

-- 删除现有函数（如果存在）
DROP FUNCTION IF EXISTS update_location_from_lat_lng() CASCADE;

-- 创建改进的触发器函数
CREATE OR REPLACE FUNCTION update_location_from_lat_lng()
RETURNS TRIGGER AS $$
BEGIN
    -- 检查经纬度是否有效且在合理范围内
    IF NEW.latitude IS NOT NULL 
       AND NEW.longitude IS NOT NULL 
       AND NEW.latitude BETWEEN -90 AND 90 
       AND NEW.longitude BETWEEN -180 AND 180 THEN
        
        -- 使用ST_SetSRID和ST_MakePoint创建PostGIS geography点
        -- 4326是WGS 84的SRID，是GPS系统使用的标准坐标参考系统
        NEW.location = ST_SetSRID(ST_MakePoint(NEW.longitude, NEW.latitude), 4326)::geography;
        
    ELSE
        -- 如果坐标无效，将location设置为NULL
        NEW.location = NULL;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为函数添加注释
COMMENT ON FUNCTION update_location_from_lat_lng() IS '触发器函数：根据经纬度自动更新PostGIS location字段，包含坐标有效性验证';

-- --------------------------------------------------------------------
-- 2. 用户地址表触发器创建
-- --------------------------------------------------------------------

-- 删除现有触发器（如果存在）
DROP TRIGGER IF EXISTS trg_update_user_address_location ON user_addresses;

-- 创建触发器：在插入或更新经纬度时自动更新location字段
CREATE TRIGGER trg_update_user_address_location
    BEFORE INSERT OR UPDATE OF latitude, longitude
    ON user_addresses
    FOR EACH ROW
    EXECUTE FUNCTION update_location_from_lat_lng();

-- 为触发器添加注释
COMMENT ON TRIGGER trg_update_user_address_location ON user_addresses IS '地址位置更新触发器：在插入或更新经纬度时自动生成PostGIS location字段';

-- --------------------------------------------------------------------
-- 触发器管理辅助SQL语句
-- 
-- 以下SQL语句用于触发器的管理和维护，可根据需要执行
-- --------------------------------------------------------------------

/*
-- 查看所有触发器状态
SELECT 
    schemaname,
    tablename,
    triggername,
    definition
FROM pg_triggers 
WHERE schemaname = 'public' 
ORDER BY tablename, triggername;

-- 查看触发器函数详情
SELECT 
    n.nspname AS schema_name,
    p.proname AS function_name,
    pg_get_function_result(p.oid) AS return_type,
    pg_get_function_arguments(p.oid) AS arguments,
    d.description
FROM pg_proc p
LEFT JOIN pg_namespace n ON p.pronamespace = n.oid
LEFT JOIN pg_description d ON p.oid = d.objoid
WHERE p.proname LIKE '%location%'
ORDER BY n.nspname, p.proname;

-- 禁用触发器（维护时使用）
-- ALTER TABLE user_addresses DISABLE TRIGGER trg_update_user_address_location;

-- 启用触发器
-- ALTER TABLE user_addresses ENABLE TRIGGER trg_update_user_address_location;

-- 删除触发器和函数（清理时使用）
-- DROP TRIGGER IF EXISTS trg_update_user_address_location ON user_addresses;
-- DROP FUNCTION IF EXISTS update_location_from_lat_lng() CASCADE;
*/

-- ====================================================================
-- 触发器文件结束
-- ====================================================================