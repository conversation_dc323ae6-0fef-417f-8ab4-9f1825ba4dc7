package repository

import (
	"context"
	"time"

	"bdb-backend/internal/model"

	"gorm.io/gorm"
)

// 地址查询的标准字段列表
var addressSelectFields = []string{
	"id", "user_id", "name", "address", "detail_address", "full_address",
	"latitude", "longitude", "is_default", "last_used_at", "created_at", "updated_at",
}

type UserAddressRepository interface {
	Create(ctx context.Context, address *model.UserAddress) error
	Find(ctx context.Context, id uint) (*model.UserAddress, error)
	Update(ctx context.Context, address *model.UserAddress) error
	Delete(ctx context.Context, id uint) error
	GetByUser(ctx context.Context, userID uint, page, pageSize int) ([]*model.UserAddress, int64, error)
	GetAllByUser(ctx context.Context, userID uint) ([]*model.UserAddress, error)
	GetDefaultByUser(ctx context.Context, userID uint) (*model.UserAddress, error)
	SetDefault(ctx context.Context, userID, addressID uint) error
	UpdateLastUsed(ctx context.Context, id uint) error
	CountByUser(ctx context.Context, userID uint) (int64, error)
}

type userAddressRepository struct {
	db *gorm.DB
}

func NewUserAddressRepository(db *gorm.DB) UserAddressRepository {
	return &userAddressRepository{db: db}
}

func (r *userAddressRepository) Create(ctx context.Context, address *model.UserAddress) error {
	return r.db.WithContext(ctx).Create(address).Error
}

func (r *userAddressRepository) Find(ctx context.Context, id uint) (*model.UserAddress, error) {
	var address model.UserAddress
	err := r.db.WithContext(ctx).
		Scopes(model.NotDeleted()).
		Select(addressSelectFields).
		First(&address, id).Error
	if err != nil {
		return nil, err
	}
	return &address, nil
}

func (r *userAddressRepository) Update(ctx context.Context, address *model.UserAddress) error {
	return r.db.WithContext(ctx).Save(address).Error
}

func (r *userAddressRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).
		Model(&model.UserAddress{}).
		Where("id = ?", id).
		Update("is_del", time.Now().Unix()).Error
}

func (r *userAddressRepository) GetByUser(ctx context.Context, userID uint, page, pageSize int) ([]*model.UserAddress, int64, error) {
	var addresses []*model.UserAddress
	var total int64

	db := r.db.WithContext(ctx).
		Model(&model.UserAddress{}).
		Scopes(model.NotDeleted()).
		Where("user_id = ?", userID)

	// 计算总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 查询数据 - 按默认地址和最后使用时间排序
	err := db.Select(addressSelectFields).
		Order("is_default DESC, last_used_at DESC NULLS LAST, created_at DESC").
		Scopes(model.Paginate(page, pageSize)).
		Find(&addresses).Error

	return addresses, total, err
}

func (r *userAddressRepository) GetAllByUser(ctx context.Context, userID uint) ([]*model.UserAddress, error) {
	var addresses []*model.UserAddress

	err := r.db.WithContext(ctx).
		Model(&model.UserAddress{}).
		Scopes(model.NotDeleted()).
		Where("user_id = ?", userID).
		Select(addressSelectFields).
		Order("is_default DESC, last_used_at DESC NULLS LAST, created_at DESC").
		Find(&addresses).Error

	return addresses, err
}

func (r *userAddressRepository) GetDefaultByUser(ctx context.Context, userID uint) (*model.UserAddress, error) {
	var address model.UserAddress
	err := r.db.WithContext(ctx).
		Scopes(model.NotDeleted()).
		Select(addressSelectFields).
		Where("user_id = ? AND is_default = ?", userID, true).
		First(&address).Error
	if err != nil {
		return nil, err
	}
	return &address, nil
}

func (r *userAddressRepository) SetDefault(ctx context.Context, userID, addressID uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 先取消该用户的所有默认地址
		if err := tx.Model(&model.UserAddress{}).
			Where("user_id = ? AND is_default = ?", userID, true).
			Update("is_default", false).Error; err != nil {
			return err
		}

		// 设置新的默认地址
		return tx.Model(&model.UserAddress{}).
			Where("id = ? AND user_id = ?", addressID, userID).
			Update("is_default", true).Error
	})
}

func (r *userAddressRepository) UpdateLastUsed(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).
		Model(&model.UserAddress{}).
		Where("id = ?", id).
		Update("last_used_at", time.Now()).Error
}

func (r *userAddressRepository) CountByUser(ctx context.Context, userID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.UserAddress{}).
		Scopes(model.NotDeleted()).
		Where("user_id = ?", userID).
		Count(&count).Error
	return count, err
}
