package model

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

// StringSlice 自定义JSON字符串数组类型
type StringSlice []string

// Value 实现 driver.Valuer 接口
func (s StringSlice) Value() (driver.Value, error) {
	if len(s) == 0 {
		return nil, nil
	}
	return json.Marshal(s)
}

// Scan 实现 sql.Scanner 接口
func (s *StringSlice) Scan(value interface{}) error {
	if value == nil {
		*s = nil
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, s)
	case string:
		return json.Unmarshal([]byte(v), s)
	default:
		return fmt.Errorf("cannot scan %T into StringSlice", value)
	}
}

// UserAddress 用户地址模型
type UserAddress struct {
	BaseModel
	SoftDelete
	UserID          uint        `gorm:"not null;index:idx_user_addresses_user_id" json:"user_id"`
	Name            string      `gorm:"type:varchar(100);not null" json:"name"`
	Address         string      `gorm:"type:varchar(255);not null" json:"address"`
	DetailAddress   string      `gorm:"type:varchar(255)" json:"detail_address"`
	FullAddress     string      `gorm:"type:varchar(500);not null" json:"full_address"`
	Latitude        float64     `gorm:"type:decimal(9,6);default:0" json:"latitude"`
	Longitude       float64     `gorm:"type:decimal(10,6);default:0" json:"longitude"`
	NavigationGuide string      `gorm:"type:text" json:"navigation_guide"`
	GuideImages     StringSlice `gorm:"type:json" json:"guide_images"`
	GuideVideos     StringSlice `gorm:"type:json" json:"guide_videos"`
	IsDefault       bool        `gorm:"default:false;index:idx_user_addresses_is_default" json:"is_default"`
	LastUsedAt      *time.Time  `gorm:"type:timestamp(0)" json:"last_used_at"`
}

func (ua UserAddress) TableName() string {
	return "user_addresses"
}
