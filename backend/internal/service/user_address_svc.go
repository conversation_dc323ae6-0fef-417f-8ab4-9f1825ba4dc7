package service

import (
	"context"
	"errors"
	"fmt"

	"bdb-backend/internal/constants"
	"bdb-backend/internal/model"
	"bdb-backend/internal/repository"
	"bdb-backend/internal/types"

	"github.com/jinzhu/copier"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

type UserAddressService interface {
	CreateAddress(ctx context.Context, userID uint, req *types.CreateAddressRequest) (*types.AddressResponse, error)
	GetAddressList(ctx context.Context, userID uint) (*types.GetAddressListResponse, error)
	GetAddressDetail(ctx context.Context, userID uint, addressID uint) (*types.AddressResponse, error)
	UpdateAddress(ctx context.Context, userID uint, addressID uint, req *types.UpdateAddressRequest) error
	DeleteAddress(ctx context.Context, userID uint, addressID uint) error
	SetDefaultAddress(ctx context.Context, userID uint, addressID uint) error
	UseAddress(ctx context.Context, userID uint, addressID uint) error
}

type userAddressService struct {
	addressRepo repository.UserAddressRepository
}

func NewUserAddressService(addressRepo repository.UserAddressRepository) UserAddressService {
	return &userAddressService{
		addressRepo: addressRepo,
	}
}

func (s *userAddressService) CreateAddress(ctx context.Context, userID uint, req *types.CreateAddressRequest) (*types.AddressResponse, error) {
	// 验证坐标
	if err := validateCoordinates(req.Latitude, req.Longitude); err != nil {
		return nil, err
	}

	// 检查地址数量限制
	count, err := s.addressRepo.CountByUser(ctx, userID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", userID).Msg("获取用户地址数量失败")
		return nil, fmt.Errorf("检查地址数量失败: %w", err)
	}

	if count >= constants.MaxAddressesPerUser {
		return nil, errors.New("地址数量已达上限")
	}

	// 创建地址模型
	var address model.UserAddress
	if err := copier.Copy(&address, req); err != nil {
		log.Error().Err(err).Msg("复制地址数据失败")
		return nil, fmt.Errorf("创建地址数据复制失败: %w", err)
	}

	address.UserID = userID

	// 转换字符串数组
	if len(req.GuideImages) > 0 {
		address.GuideImages = model.StringSlice(req.GuideImages)
	}
	if len(req.GuideVideos) > 0 {
		address.GuideVideos = model.StringSlice(req.GuideVideos)
	}

	// 如果是第一个地址，自动设为默认
	if count == 0 {
		address.IsDefault = true
	}

	// 保存地址
	if err := s.addressRepo.Create(ctx, &address); err != nil {
		log.Error().Err(err).Uint("user_id", userID).Msg("创建地址失败")
		return nil, fmt.Errorf("创建地址失败: %w", err)
	}

	// 返回响应
	return s.convertAddressToResponse(&address), nil
}

func (s *userAddressService) GetAddressList(ctx context.Context, userID uint) (*types.GetAddressListResponse, error) {
	addresses, err := s.addressRepo.GetAllByUser(ctx, userID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", userID).Msg("获取地址列表失败")
		return nil, fmt.Errorf("获取地址列表失败: %w", err)
	}

	// 转换响应
	addressList := make([]*types.AddressResponse, 0, len(addresses))
	for _, addr := range addresses {
		addressList = append(addressList, s.convertAddressToResponse(addr))
	}

	return &types.GetAddressListResponse{
		Addresses: addressList,
	}, nil
}

func (s *userAddressService) GetAddressDetail(ctx context.Context, userID uint, addressID uint) (*types.AddressResponse, error) {
	address, err := s.validateAddressOwnership(ctx, userID, addressID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", userID).Uint("address_id", addressID).Msg("验证地址所有权失败")
		return nil, err
	}

	return s.convertAddressToResponse(address), nil
}

func (s *userAddressService) UpdateAddress(ctx context.Context, userID uint, addressID uint, req *types.UpdateAddressRequest) error {
	// 验证坐标（如果提供了新的坐标）
	if req.Latitude != 0 || req.Longitude != 0 {
		if err := validateCoordinates(req.Latitude, req.Longitude); err != nil {
			return err
		}
	}

	// 验证地址所有权
	address, err := s.validateAddressOwnership(ctx, userID, addressID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", userID).Uint("address_id", addressID).Msg("验证地址所有权失败")
		return err
	}

	// 更新地址信息
	if err := copier.CopyWithOption(address, req, copier.Option{
		IgnoreEmpty: true,
		DeepCopy:    true,
	}); err != nil {
		log.Error().Err(err).Uint("address_id", addressID).Msg("复制地址更新数据失败")
		return fmt.Errorf("更新地址数据复制失败: %w", err)
	}

	// 手动处理字符串数组字段（copier可能无法正确处理）
	if req.GuideImages != nil {
		address.GuideImages = model.StringSlice(req.GuideImages)
	}
	if req.GuideVideos != nil {
		address.GuideVideos = model.StringSlice(req.GuideVideos)
	}

	// 保存更新
	if err := s.addressRepo.Update(ctx, address); err != nil {
		log.Error().Err(err).Uint("address_id", addressID).Msg("更新地址失败")
		return fmt.Errorf("更新地址失败: %w", err)
	}

	return nil
}

func (s *userAddressService) DeleteAddress(ctx context.Context, userID uint, addressID uint) error {
	// 验证地址所有权
	_, err := s.validateAddressOwnership(ctx, userID, addressID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", userID).Uint("address_id", addressID).Msg("验证地址所有权失败")
		return err
	}

	// 删除地址
	if err := s.addressRepo.Delete(ctx, addressID); err != nil {
		log.Error().Err(err).Uint("address_id", addressID).Msg("删除地址失败")
		return fmt.Errorf("删除地址失败: %w", err)
	}

	return nil
}

func (s *userAddressService) SetDefaultAddress(ctx context.Context, userID uint, addressID uint) error {
	// 验证地址所有权
	_, err := s.validateAddressOwnership(ctx, userID, addressID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", userID).Uint("address_id", addressID).Msg("验证地址所有权失败")
		return err
	}

	// 设置默认地址
	if err := s.addressRepo.SetDefault(ctx, userID, addressID); err != nil {
		log.Error().Err(err).Uint("user_id", userID).Uint("address_id", addressID).Msg("设置默认地址失败")
		return fmt.Errorf("设置默认地址失败: %w", err)
	}

	return nil
}

func (s *userAddressService) UseAddress(ctx context.Context, userID uint, addressID uint) error {
	// 验证地址所有权
	_, err := s.validateAddressOwnership(ctx, userID, addressID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", userID).Uint("address_id", addressID).Msg("验证地址所有权失败")
		return err
	}

	// 更新最后使用时间
	if err := s.addressRepo.UpdateLastUsed(ctx, addressID); err != nil {
		log.Error().Err(err).Uint("address_id", addressID).Msg("更新地址使用时间失败")
		return fmt.Errorf("更新地址使用时间失败: %w", err)
	}

	return nil
}

// validateCoordinates 验证坐标是否在合理范围内
func validateCoordinates(lat, lng float64) error {
	if lat < constants.MinLatitude || lat > constants.MaxLatitude {
		return errors.New("纬度超出有效范围")
	}
	if lng < constants.MinLongitude || lng > constants.MaxLongitude {
		return errors.New("经度超出有效范围")
	}

	// 可选：更严格的中国境内验证
	// if lat < constants.ChinaMinLatitude || lat > constants.ChinaMaxLatitude ||
	//    lng < constants.ChinaMinLongitude || lng > constants.ChinaMaxLongitude {
	//    return errors.New("坐标不在中国境内")
	// }

	return nil
}

// validateAddressOwnership 验证地址所有权并返回地址信息
func (s *userAddressService) validateAddressOwnership(ctx context.Context, userID, addressID uint) (*model.UserAddress, error) {
	address, err := s.addressRepo.Find(ctx, addressID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("地址不存在")
		}
		return nil, fmt.Errorf("获取地址失败: %w", err)
	}

	if address.UserID != userID {
		return nil, errors.New("无权访问该地址")
	}

	return address, nil
}

func (s *userAddressService) convertAddressToResponse(address *model.UserAddress) *types.AddressResponse {
	var response types.AddressResponse
	if err := copier.Copy(&response, address); err != nil {
		log.Error().Err(err).Uint("address_id", address.ID).Msg("转换地址响应失败")
		// 如果copier失败，手动构建响应
		response = types.AddressResponse{
			ID:              address.ID,
			Name:            address.Name,
			Address:         address.Address,
			DetailAddress:   address.DetailAddress,
			FullAddress:     address.FullAddress,
			Latitude:        address.Latitude,
			Longitude:       address.Longitude,
			NavigationGuide: address.NavigationGuide,
			IsDefault:       address.IsDefault,
			CreatedAt:       types.DateTime(address.CreatedAt),
			UpdatedAt:       types.DateTime(address.UpdatedAt),
		}
	}

	// 手动处理字符串数组字段
	if address.GuideImages != nil {
		response.GuideImages = []string(address.GuideImages)
	} else {
		response.GuideImages = []string{}
	}

	if address.GuideVideos != nil {
		response.GuideVideos = []string(address.GuideVideos)
	} else {
		response.GuideVideos = []string{}
	}

	// 处理最后使用时间
	if address.LastUsedAt != nil {
		lastUsedAt := types.DateTime{Time: *address.LastUsedAt}
		response.LastUsedAt = &lastUsedAt
	}

	return &response
}
