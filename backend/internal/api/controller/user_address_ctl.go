package controller

import (
	"bdb-backend/internal/service"
	"bdb-backend/internal/types"
	"bdb-backend/pkg/logger"
	"bdb-backend/pkg/response"
	"bdb-backend/pkg/validator"

	"github.com/gin-gonic/gin"
)

// UserAddressController 用户地址控制器
type UserAddressController struct {
	userAddressService service.UserAddressService
	validator          validator.Validator
}

// NewUserAddressController 创建用户地址控制器
func NewUserAddressController(
	userAddressService service.UserAddressService,
	validator validator.Validator,
) *UserAddressController {
	return &UserAddressController{
		userAddressService: userAddressService,
		validator:          validator,
	}
}

// CreateAddress 创建地址
func (c *UserAddressController) CreateAddress(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req types.CreateAddressRequest
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	address, err := c.userAddressService.CreateAddress(ctx, userID, &req)
	if err != nil {
		logger.ErrorCtx(ctx, "创建地址失败", err, "user_id", userID)
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, address)
}

// GetAddressList 获取地址列表
func (c *UserAddressController) GetAddressList(ctx *gin.Context) {
	userID := GetUserID(ctx)

	resp, err := c.userAddressService.GetAddressList(ctx, userID)
	if err != nil {
		logger.ErrorCtx(ctx, "获取地址列表失败", err, "user_id", userID)
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, resp)
}

// GetAddressDetail 获取地址详情
func (c *UserAddressController) GetAddressDetail(ctx *gin.Context) {
	userID := GetUserID(ctx)

	addressIDStr := ctx.Param("id")
	addressID, err := parseUint(addressIDStr)
	if err != nil {
		response.BadRequest(ctx, "无效的地址ID")
		return
	}

	address, err := c.userAddressService.GetAddressDetail(ctx, userID, addressID)
	if err != nil {
		logger.ErrorCtx(ctx, "获取地址详情失败", err, "user_id", userID, "address_id", addressID)
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, address)
}

// UpdateAddress 更新地址
func (c *UserAddressController) UpdateAddress(ctx *gin.Context) {
	userID := GetUserID(ctx)

	addressIDStr := ctx.Param("id")
	addressID, err := parseUint(addressIDStr)
	if err != nil {
		response.BadRequest(ctx, "无效的地址ID")
		return
	}

	var req types.UpdateAddressRequest
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	err = c.userAddressService.UpdateAddress(ctx, userID, addressID, &req)
	if err != nil {
		logger.ErrorCtx(ctx, "更新地址失败", err, "user_id", userID, "address_id", addressID)
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, gin.H{"message": "地址更新成功"})
}

// DeleteAddress 删除地址
func (c *UserAddressController) DeleteAddress(ctx *gin.Context) {
	userID := GetUserID(ctx)

	addressIDStr := ctx.Param("id")
	addressID, err := parseUint(addressIDStr)
	if err != nil {
		response.BadRequest(ctx, "无效的地址ID")
		return
	}

	err = c.userAddressService.DeleteAddress(ctx, userID, addressID)
	if err != nil {
		logger.ErrorCtx(ctx, "删除地址失败", err, "user_id", userID, "address_id", addressID)
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, gin.H{"message": "地址删除成功"})
}

// SetDefaultAddress 设置默认地址
func (c *UserAddressController) SetDefaultAddress(ctx *gin.Context) {
	userID := GetUserID(ctx)

	addressIDStr := ctx.Param("id")
	addressID, err := parseUint(addressIDStr)
	if err != nil {
		response.BadRequest(ctx, "无效的地址ID")
		return
	}

	err = c.userAddressService.SetDefaultAddress(ctx, userID, addressID)
	if err != nil {
		logger.ErrorCtx(ctx, "设置默认地址失败", err, "user_id", userID, "address_id", addressID)
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, gin.H{"message": "默认地址设置成功"})
}

// UseAddress 使用地址（更新最后使用时间）
func (c *UserAddressController) UseAddress(ctx *gin.Context) {
	userID := GetUserID(ctx)

	addressIDStr := ctx.Param("id")
	addressID, err := parseUint(addressIDStr)
	if err != nil {
		response.BadRequest(ctx, "无效的地址ID")
		return
	}

	err = c.userAddressService.UseAddress(ctx, userID, addressID)
	if err != nil {
		logger.ErrorCtx(ctx, "使用地址失败", err, "user_id", userID, "address_id", addressID)
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, gin.H{"message": "地址使用记录更新成功"})
}
