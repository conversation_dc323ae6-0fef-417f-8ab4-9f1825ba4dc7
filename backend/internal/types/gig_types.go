package types

import (
	"bdb-backend/internal/constants"
	"time"
)

// CreateGigReq 创建零工的请求体
type CreateGigReq struct {
	Title         string   `json:"title" binding:"required,min=2,max=100"`
	Description   string   `json:"description" binding:"required,min=10,max=1000"`
	Salary        int      `json:"salary" binding:"required,gt=0"`
	SalaryUnit    int16    `json:"salary_unit" binding:"required,oneof=1 2 3 4"`
	Settlement    int16    `json:"settlement" binding:"required,oneof=1 2 3 4"`
	PeopleCount   int      `json:"people_count" binding:"required,gt=0"`
	StartTime     string   `json:"start_time" binding:"required"`
	EndTime       string   `json:"end_time" binding:"required"`
	AddressName   string   `json:"address_name" binding:"required"`
	Address       string   `json:"address" binding:"required"`
	DetailAddress string   `json:"detail_address"`
	Latitude      float64  `json:"latitude" binding:"required,latitude"`
	Longitude     float64  `json:"longitude" binding:"required,longitude"`
	Gender        int16    `json:"gender" binding:"oneof=0 1 2"`
	AgeMin        int      `json:"age_min" binding:"required,gte=16,lte=100"`
	AgeMax        int      `json:"age_max" binding:"required,gte=16,lte=100"`
	Experience    int16    `json:"experience" binding:"gte=0"`
	Education     int16    `json:"education" binding:"gte=0"`
	Skills        string   `json:"skills"`
	ContactName   string   `json:"contact_name" binding:"required"`
	ContactPhone  string   `json:"contact_phone" binding:"required,len=11"`
	IsUrgent      bool     `json:"is_urgent"`
	CompanyName   string   `json:"company_name"`
	Tags          []string `json:"tags"`
	Images        []string `json:"images"`
	ApprovalMode  string   `json:"approval_mode" binding:"oneof=manual auto"`
	CheckInMethod string   `json:"check_in_method"`
}

// GigDetailResp 零工详情的响应体
type GigDetailResp struct {
	ID                 uint                `json:"id"`
	UserID             uint                `json:"user_id"`
	Title              string              `json:"title"`
	Description        string              `json:"description"`
	Salary             int                 `json:"salary"`
	SalaryUnit         int16               `json:"salary_unit"`
	Settlement         int16               `json:"settlement"`
	PeopleCount        int                 `json:"people_count"`
	CurrentPeopleCount int                 `json:"current_people_count"`
	StartTime          time.Time           `json:"start_time"`
	EndTime            time.Time           `json:"end_time"`
	WorkDuration       int                 `json:"work_duration"`
	AddressName        string              `json:"address_name"`
	Address            string              `json:"address"`
	DetailAddress      string              `json:"detail_address"`
	FullAddress        string              `json:"full_address"`
	Latitude           float64             `json:"latitude"`
	Longitude          float64             `json:"longitude"`
	Gender             int16               `json:"gender"`
	AgeMin             int                 `json:"age_min"`
	AgeMax             int                 `json:"age_max"`
	Experience         int16               `json:"experience"`
	Education          int16               `json:"education"`
	Skills             string              `json:"skills"`
	ContactName        string              `json:"contact_name"`
	ContactPhone       string              `json:"contact_phone"`
	Status             constants.GigStatus `json:"status"`
	ApprovalMode       string              `json:"approval_mode"`
	CheckInMethod      string              `json:"check_in_method"`
	IsUrgent           bool                `json:"is_urgent"`
	CompanyName        string              `json:"company_name"`
	Tags               []string            `json:"tags"`
	Images             []string            `json:"images"`
	CreatedAt          time.Time           `json:"created_at"`
	Publisher          UserProfileResponse `json:"publisher"`
	HasApplied         bool                `json:"has_applied,omitempty"`
}

// PublisherInfo 发布者信息
type PublisherInfo struct {
	UserID   uint   `json:"user_id"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
}

// GigListReq 获取零工列表的请求体
type GigListReq struct {
	PaginationReq
	Keyword     string  `form:"keyword"`
	SalaryMin   int     `form:"salary_min"`
	SalaryMax   int     `form:"salary_max"`
	DistanceMax int     `form:"distance_max"` // in meters
	UserLat     float64 `form:"user_lat"`
	UserLon     float64 `form:"user_lon"`
	Settlement  []int16 `form:"settlement"`
	Experience  []int16 `form:"experience"`
	Education   []int16 `form:"education"`
	Gender      int16   `form:"gender"`
	IsUrgent    bool    `form:"is_urgent"`
	SortBy      string  `form:"sort_by" binding:"omitempty,oneof=recommend latest distance salary"`
}

// ApplyGigReq 申请零工的请求体
type ApplyGigReq struct {
	GigID         uint   `json:"gig_id" binding:"required"`
	ContactName   string `json:"contact_name" binding:"required"`
	ContactPhone  string `json:"contact_phone" binding:"required"`
	HasExperience bool   `json:"has_experience"`
	Message       string `json:"message,omitempty"  binding:"max=200"`
}

// GigApplicationResp 零工申请的响应体
type GigApplicationResp struct {
	ID            uint                           `json:"id"`
	GigID         uint                           `json:"gig_id"`
	UserID        uint                           `json:"user_id"`
	Status        constants.GigApplicationStatus `json:"status"`
	Message       string                         `json:"message"`
	ContactName   string                         `json:"contact_name"`
	ContactPhone  string                         `json:"contact_phone"`
	HasExperience bool                           `json:"has_experience"`
	ReviewedAt    *time.Time                     `json:"reviewed_at"`
	Reason        string                         `json:"reason"`
	CheckInAt     *time.Time                     `json:"check_in_at"`
	CheckOutAt    *time.Time                     `json:"check_out_at"`
	CreatedAt     time.Time                      `json:"created_at"`
	Gig           *GigDetailResp                 `json:"gig,omitempty"`
	GigInfo       *GigInfoForApplication         `json:"gig_info,omitempty"`
	ApplicantInfo *PublisherInfo                 `json:"applicant_info,omitempty"`
}

// GigInfoForApplication 申请中关联的零工简要信息
type GigInfoForApplication struct {
	ID         uint   `json:"id"`
	Title      string `json:"title"`
	Salary     int    `json:"salary"`
	SalaryUnit int    `json:"salary_unit"`
}

// ReviewApplicationReq 审核零工申请的请求体
type ReviewApplicationReq struct {
	ApplicationID uint                           `json:"application_id" binding:"required"`
	NewStatus     constants.GigApplicationStatus `json:"new_status" binding:"required,oneof=Confirmed Rejected"`
	Reason        string                         `json:"reason" binding:"max=500"`
}

// UpdateApplicationStatusReq 更新申请状态的请求体 (申请人操作)
type UpdateApplicationStatusReq struct {
	ApplicationID uint                           `json:"application_id" binding:"required"`
	NewStatus     constants.GigApplicationStatus `json:"new_status" binding:"required,oneof=Withdrawn Cancelled"`
}

// UserGigStats 用户零工统计信息
type UserGigStats struct {
	PublishedCount int64   `json:"published_count"` // 发布的零工数量
	CompletedCount int64   `json:"completed_count"` // 完成的零工数量
	AppliedCount   int64   `json:"applied_count"`   // 申请的零工数量
	AcceptedCount  int64   `json:"accepted_count"`  // 被接受的申请数量
	TotalEarnings  int64   `json:"total_earnings"`  // 总收入（分）
	AverageRating  float64 `json:"average_rating"`  // 平均评分
}

// MonthlyStatsReq 月度统计请求
type MonthlyStatsReq struct {
	Year  int `form:"year" binding:"required"`
	Month int `form:"month" binding:"required,min=1,max=12"`
}

// MonthlyStatsResp 月度统计响应
type MonthlyStatsResp struct {
	Year          int        `json:"year"`
	Month         int        `json:"month"`
	TotalGigs     int64      `json:"total_gigs"`
	CompletedGigs int64      `json:"completed_gigs"`
	TotalEarnings int64      `json:"total_earnings"`
	DailyStats    []DailyGig `json:"daily_stats"`
}

// DailyGigReq 日期查询请求
type DailyGigReq struct {
	Date string `form:"date" binding:"required"` // YYYY-MM-DD format
}

// DailyGig 日期零工数据
type DailyGig struct {
	Date     string               `json:"date"`
	GigCount int64                `json:"gig_count"`
	Earnings int64                `json:"earnings"`
	Gigs     []GigDetailResp      `json:"gigs,omitempty"`
	Apps     []GigApplicationResp `json:"applications,omitempty"`
}

// DateRangeReq 日期范围请求
type DateRangeReq struct {
	StartDate string `form:"start_date" binding:"required"` // YYYY-MM-DD format
	EndDate   string `form:"end_date" binding:"required"`   // YYYY-MM-DD format
}
