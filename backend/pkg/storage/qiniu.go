package storage

import (
	"context"
	"fmt"
	"io"
	"strings"
	"time"

	"bdb-backend/pkg/config"

	"github.com/qiniu/go-sdk/v7/auth/qbox"
	"github.com/qiniu/go-sdk/v7/storage"
)

// QiniuStorage 七牛云存储服务
type QiniuStorage struct {
	accessKey    string
	secretKey    string
	tokenExpires int64
	bucket       string
	domain       string
	mac          *qbox.Mac
	cfg          *storage.Config
}

// NewQiniuStorage 创建七牛云存储服务实例
func NewQiniuStorage(config config.StorageConfig) Storage {
	mac := qbox.NewMac(config.AccessKey, config.SecretKey)

	cfg := &storage.Config{
		Zone:          &storage.ZoneHuabei, // 华北机房
		UseCdnDomains: false,
		UseHTTPS:      true,
	}

	return &QiniuStorage{
		accessKey:    config.AccessKey,
		secretKey:    config.SecretKey,
		tokenExpires: config.TokenExpires,
		bucket:       config.Bucket,
		domain:       config.Domain,
		mac:          mac,
		cfg:          cfg,
	}
}

// GetDomain returns the domain of the qiniu storage
func (q *QiniuStorage) GetDomain() string {
	return q.domain
}

// UploadFile 上传文件
func (q *QiniuStorage) UploadFile(key string, data io.Reader, size int64) (*UploadResult, error) {
	return q.UploadFileWithMimeType(key, data, size, "")
}

// UploadFileWithMimeType 上传文件（指定MIME类型）
func (q *QiniuStorage) UploadFileWithMimeType(key string, data io.Reader, size int64, mimeType string) (*UploadResult, error) {
	// 生成上传凭证
	putPolicy := storage.PutPolicy{
		Scope: q.bucket,
	}

	if mimeType != "" {
		putPolicy.MimeLimit = mimeType
	}

	upToken := putPolicy.UploadToken(q.mac)

	// 构建表单上传的对象
	formUploader := storage.NewFormUploader(q.cfg)
	ret := storage.PutRet{}
	putExtra := storage.PutExtra{}

	// 执行上传
	err := formUploader.Put(context.Background(), &ret, upToken, key, data, size, &putExtra)
	if err != nil {
		return nil, fmt.Errorf("upload file failed: %w", err)
	}

	// 构建访问URL
	url := q.buildDownloadURL(key)

	return &UploadResult{
		Key:      ret.Key,
		Hash:     ret.Hash,
		Size:     size,
		MimeType: mimeType,
		URL:      url,
	}, nil
}

// DeleteFile 删除文件
func (q *QiniuStorage) DeleteFile(key string) error {
	bucketManager := storage.NewBucketManager(q.mac, q.cfg)
	err := bucketManager.Delete(q.bucket, key)
	if err != nil {
		return fmt.Errorf("delete file failed: %w", err)
	}
	return nil
}

// GetFileInfo 获取文件信息
func (q *QiniuStorage) GetFileInfo(key string) (*FileInfo, error) {
	bucketManager := storage.NewBucketManager(q.mac, q.cfg)
	fileInfo, err := bucketManager.Stat(q.bucket, key)
	if err != nil {
		return nil, fmt.Errorf("get file info failed: %w", err)
	}

	url := q.buildDownloadURL(key)

	return &FileInfo{
		Key:        key,
		Hash:       fileInfo.Hash,
		Size:       fileInfo.Fsize,
		MimeType:   fileInfo.MimeType,
		PutTime:    time.Unix(0, fileInfo.PutTime*100), // 七牛返回的是100纳秒为单位
		ModifyTime: time.Unix(0, fileInfo.PutTime*100),
		URL:        url,
	}, nil
}

// GenerateUploadToken 生成上传令牌
func (q *QiniuStorage) GenerateUploadToken(key string, expires time.Duration) (string, error) {
	putPolicy := storage.PutPolicy{
		Scope:   q.bucket,
		Expires: uint64(q.tokenExpires),
	}

	upToken := putPolicy.UploadToken(q.mac)
	return upToken, nil
}

// GenerateDownloadURL 生成下载URL
func (q *QiniuStorage) GenerateDownloadURL(key string, expires time.Duration) (string, error) {
	domain := q.domain
	if !strings.HasPrefix(domain, "http://") && !strings.HasPrefix(domain, "https://") {
		domain = "https://" + domain
	}

	privateAccessURL := storage.MakePrivateURL(q.mac, domain, key, expires.Nanoseconds()/1e9)
	return privateAccessURL, nil
}

// ListFiles 列出文件
func (q *QiniuStorage) ListFiles(prefix string, limit int) ([]*FileInfo, error) {
	bucketManager := storage.NewBucketManager(q.mac, q.cfg)

	entries, _, _, hasNext, err := bucketManager.ListFiles(q.bucket, prefix, "", "", limit)
	if err != nil {
		return nil, fmt.Errorf("list files failed: %w", err)
	}

	var fileInfos []*FileInfo
	for _, entry := range entries {
		url := q.buildDownloadURL(entry.Key)

		fileInfo := &FileInfo{
			Key:        entry.Key,
			Hash:       entry.Hash,
			Size:       entry.Fsize,
			MimeType:   entry.MimeType,
			PutTime:    time.Unix(0, entry.PutTime*100),
			ModifyTime: time.Unix(0, entry.PutTime*100),
			URL:        url,
		}
		fileInfos = append(fileInfos, fileInfo)
	}

	_ = hasNext // 可以用于分页
	return fileInfos, nil
}

// buildDownloadURL 构建下载URL
func (q *QiniuStorage) buildDownloadURL(key string) string {
	domain := q.domain
	if !strings.HasPrefix(domain, "http://") && !strings.HasPrefix(domain, "https://") {
		domain = "https://" + domain
	}

	if strings.HasSuffix(domain, "/") {
		return domain + key
	}
	return domain + "/" + key
}

// GenerateUploadPolicy 生成上传策略
func (q *QiniuStorage) GenerateUploadPolicy(policy *UploadPolicy) (string, error) {
	putPolicy := storage.PutPolicy{
		Scope:               policy.Scope,
		Expires:             uint64(policy.Deadline),
		InsertOnly:          uint16(policy.InsertOnly),
		SaveKey:             policy.SaveKey,
		EndUser:             policy.EndUser,
		ReturnURL:           policy.ReturnURL,
		ReturnBody:          policy.ReturnBody,
		CallbackURL:         policy.CallbackURL,
		CallbackBody:        policy.CallbackBody,
		CallbackBodyType:    policy.CallbackBodyType,
		PersistentOps:       policy.PersistentOps,
		PersistentNotifyURL: policy.PersistentNotifyURL,
		PersistentPipeline:  policy.PersistentPipeline,
		FsizeMin:            policy.FsizeMin,
		FsizeLimit:          policy.FsizeLimit,
		DetectMime:          policy.DetectMime,
		MimeLimit:           policy.MimeLimit,
	}

	return putPolicy.UploadToken(q.mac), nil
}

// BatchDelete 批量删除文件
func (q *QiniuStorage) BatchDelete(keys []string) error {
	bucketManager := storage.NewBucketManager(q.mac, q.cfg)

	deleteOps := make([]string, len(keys))
	for i, key := range keys {
		deleteOps[i] = storage.URIDelete(q.bucket, key)
	}

	rets, err := bucketManager.Batch(deleteOps)
	if err != nil {
		return fmt.Errorf("batch delete failed: %w", err)
	}

	// 检查每个操作的结果
	for i, ret := range rets {
		if ret.Code != 200 {
			return fmt.Errorf("delete file %s failed: code=%d, error=%s", keys[i], ret.Code, ret.Data.Error)
		}
	}

	return nil
}

// CopyFile 复制文件
func (q *QiniuStorage) CopyFile(srcKey, destKey string) error {
	bucketManager := storage.NewBucketManager(q.mac, q.cfg)
	err := bucketManager.Copy(q.bucket, srcKey, q.bucket, destKey, false)
	if err != nil {
		return fmt.Errorf("copy file failed: %w", err)
	}
	return nil
}

// MoveFile 移动文件
func (q *QiniuStorage) MoveFile(srcKey, destKey string) error {
	bucketManager := storage.NewBucketManager(q.mac, q.cfg)
	err := bucketManager.Move(q.bucket, srcKey, q.bucket, destKey, false)
	if err != nil {
		return fmt.Errorf("move file failed: %w", err)
	}
	return nil
}
