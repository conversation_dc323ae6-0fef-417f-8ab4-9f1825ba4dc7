import {
  Preset,
  defineConfig,
  presetAttributify,
  presetIcons,
  transformerDirectives,
  transformerVariantGroup,
} from "unocss";

import {
  presetApplet,
  presetRemRpx,
  transformerApplet,
  transformerAttributify,
} from "unocss-applet";

const isH5 = process.env?.UNI_PLATFORM === "h5";
const isMp = process.env?.UNI_PLATFORM?.startsWith("mp") ?? false;

const presets: Preset[] = [];
if (!isMp) {
  presets.push(presetAttributify());
}
if (!isH5) {
  presets.push(presetRemRpx());
}
export default defineConfig({
  presets: [
    presetApplet({ enable: !isH5 }),
    ...presets,
    presetIcons({
      scale: 1.2,
      warn: true,
      extraProperties: {
        display: "inline-block",
        "vertical-align": "middle",
      },
    }),
  ],
  shortcuts: [
    {
      "center": "flex justify-center items-center",
      "between": "flex justify-between items-center",
      "around": "flex justify-around items-center",
      'interactive-scale': 'transition-transform ease-in-out active:scale-95',
    }
  ],
  transformers: [
    transformerDirectives(),
    transformerVariantGroup(),
    transformerAttributify({
      prefixedOnly: true,
      prefix: "fg",
    }),
    transformerApplet(),
  ],
  rules: [
    ["primary-50", { 'color': "var(--primary-50)" }],
    ["primary-100", { 'color': "var(--primary-100)" }],
    ["primary-200", { 'color': "var(--primary-200)" }],
    ["primary-300", { 'color': "var(--primary-300)" }],
    ["primary-400", { 'color': "var(--primary-400)" }],
    ["primary-500", { 'color': "var(--primary-500)" }],
    ["primary-600", { 'color': "var(--primary-600)" }],
    ["primary-700", { 'color': "var(--primary-700)" }],
    ["primary-800", { 'color': "var(--primary-800)" }],
    ["primary-900", { 'color': "var(--primary-900)" }],
    ["primary-950", { 'color': "var(--primary-950)" }],
    ["primary", { 'color': "var(--primary)" }],
    ["text-base", { 'color': "var(--text-base)" }],
    ["text-secondary", { 'color': "var(--text-secondary)" }],
    ["text-info", { 'color': "var(--text-info)" }],
    ["text-grey", { 'color': "var(--text-grey)" }],
    ["text-disable", { 'color': "var(--text-disable)" }],
    ["text-inverse", { 'color': "var(--text-inverse)" }],
    ["text-red", { 'color': "var(--text-red)" }],
    ["text-green", { 'color': "var(--text-green)" }],
    ["text-blue", { 'color': "var(--text-blue)" }],
    ["text-yellow", { 'color': "var(--text-yellow)" }],
    ["text-purple", { 'color': "var(--text-purple)" }],
    ["text-money", { 'color': "var(--text-red)", 'font-weight': '500', 'font-size': '36rpx' }],
    ["shadow-card", { 'box-shadow': "0 4rpx 8rpx rgba(195, 195, 210, 0.1)" }],
    ["bg-page", { "background-color": "var(--bg-page)" }],
    ["bg-card", { "background-color": "var(--bg-card)" }],
    ["bg-tag", { "background-color": "var(--bg-tag)" }],
    ["bg-search", { "background-color": "var(--bg-search)" }],
    ["bg-primary", { "background-color": "var(--primary)" }],
    ["bg-disable", { "background-color": "var(--bg-disabled)" }],
    ["bg-gray", { "background-color": "var(--bg-gray)" }],
    ["bg-btn-cancel", { "background-color": "var(--bg-search)" }],
    ["bg-btn-disabled", { "background-color": "var(--bg-tag)" }],
    ["bg-primary-gradient", { "background": "linear-gradient(135deg, var(--primary), #ff8533)" }],
    ["card", {
      "background-color": "#feffff",
      "border-radius": "24rpx",
      "box-shadow": "0 4rpx 8rpx rgba(195, 195, 210, 0.1)",
      "padding": "28rpx",
    },
    ],
    [
      "card-title",
      {
        "font-size": "32rpx",
        "font-weight": "500",
        "margin-bottom": "24rpx",
        "display": "flex",
        "align-items": "center",
        "justify-content": "space-between",
      },
    ],
    [
      "flex-x",
      {
        "display": "flex",
        "flex-direction": "row",
        "flex-wrap": "wrap",
        "flex-grow": 1,
        "flex-shrink": 1,
        "flex-basis": "auto",
      },
    ],
    [
      "flex-y",
      {
        "display": "flex",
        "flex-direction": "column",
      },
    ],
    [
      "flex-x-center",
      {
        "display": "flex",
        "flex-direction": "row",
        "align-items": "center",
      },
    ],
    [
      "flex-x-between",
      {
        "display": "flex",
        "flex-direction": "row",
        "justify-content": "space-between",
        "align-items": "center",
      },
    ],
    [
      "flex-x-around",
      {
        "display": "flex",
        "flex-direction": "row",
        "justify-content": "space-around",
        "align-items": "center",
      },
    ],
    [
      "flex-y-center",
      {
        "display": "flex",
        "flex-direction": "column",
        "justify-content": "center",
        "align-items": "center",
      },
    ],
    [
      "flex-y-between",
      {
        "display": "flex",
        "flex-direction": "column",
        "justify-content": "space-between",
        "align-items": "center",
      },
    ],
    ["safe-area", { "padding-bottom": "env(safe-area-inset-bottom)" }],
    ["text-title", { "font-size": "40rpx", 'font-weight': '500' }],
    ["text-subtitle", { "font-size": "36rpx", 'font-weight': '500' }],
  ],
});