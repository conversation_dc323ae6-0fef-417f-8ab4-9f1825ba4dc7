# HTTP 请求层使用指南

## 📋 概览

经过优化后，项目的HTTP请求层分为两种使用模式：

### 🔄 useRequest 模式（查询数据）
用于获取列表、详情等数据，支持响应式状态管理：

```typescript
// 服务层封装
export const useUserProfile = () => {
  const { data, loading, error, send } = useRequest(UserApi.getProfile(), {
    immediate: true
  })
  
  return {
    userProfile: data, // 直接获取业务数据
    loading,
    error,
    refresh: send
  }
}
```

### ⚡ callApi 模式（操作数据）
用于表单提交、数据更新等操作，自动处理UI反馈：

```typescript
// 页面组件中使用
const handleSubmit = async () => {
  const result = await callApi(() => GigApi.create(formData), {
    showLoading: true,
    loadingText: '发布中...',
    showSuccessToast: true,
    successText: '发布成功',
    onSuccess: (data) => {
      console.log('创建的零工ID:', data.gigId)
      uni.navigateBack()
    }
  })
  
  if (result.success) {
    // 操作成功，result.data 包含业务数据
    console.log('成功:', result.data)
  } else {
    // 操作失败，错误已自动显示toast
    console.log('失败:', result.message)
  }
}
```

## 🎯 核心改进

### 1. 统一返回格式
- 所有API统一返回 `{code, data, message}` 格式
- 移除了 `dataOnly` 模式的复杂性

### 2. callApi 智能处理
- **自动业务逻辑判断**：根据 `code === 0` 自动处理成功/失败
- **自动UI反馈**：成功时显示成功toast，失败时显示错误toast
- **简化业务代码**：无需手动判断业务状态码

### 3. useRequest 数据简化
- 通过 `transformData` 直接获取业务数据
- 无需在组件中解构 `response.data`

## 📚 使用示例

### 基础callApi用法

```typescript
// 简单操作
const result = await callApi(() => UserApi.updateProfile(userData))
if (result.success) {
  // 自动处理成功逻辑
}

// 带loading的操作
await callApi(() => GigApi.delete(gigId), {
  showLoading: true,
  loadingText: '删除中...',
  showSuccessToast: true,
  successText: '删除成功'
})

// 自定义成功处理
await callApi(() => AuthApi.wechatLogin(loginData), {
  showLoading: true,
  onSuccess: (data) => {
    userStore.setUserInfo(data.user, data.access_token)
    uni.switchTab({ url: '/pages/home/<USER>' })
  }
})
```

### useRequest响应式数据

```typescript
// 用户信息
const { data: userProfile, loading, refresh } = useRequest(UserApi.getProfile())

// 零工列表
const { data: gigList, loading, error } = useRequest(
  GigApi.list({ page: 1, page_size: 10 }),
  { immediate: true }
)

// 监听参数变化
const searchParams = ref({ keyword: '' })
const { data: searchResults } = useRequest(
  () => GigApi.list(searchParams.value),
  { watch: [searchParams] }
)
```

## 🔧 API层配置

### 查询型API（用于useRequest）
```typescript
static getProfile = () => {
  return request.Get<ApiResponse<UserInfo>>("/users/profile", {
    transformData: (response: ApiResponse<UserInfo>) => response.data
  })
}
```

### 操作型API（用于callApi）
```typescript
static create = (data: CreateGigRequest) => {
  return request.Post<ApiResponse<{gigId: number}>>("/gigs", data)
}
```

## 🚀 迁移指南

### 从旧的callApi迁移

```typescript
// 之前需要手动判断
const response = await callApi(...)
if (response.success && response.data?.code === 0) {
  // 处理成功逻辑
} else {
  // 处理失败逻辑
}

// 现在直接使用
const result = await callApi(..., { showSuccessToast: true })
if (result.success) {
  // 直接使用 result.data（已是业务数据）
}
```

### Services层无需改动

由于添加了 `transformData`，现有的services层代码无需修改：

```typescript
// 这些代码保持不变
const { data: gigList } = useRequest(GigApi.list(params))
// gigList 直接是业务数据数组，不是 {code, data, message}
```

## ✅ 最佳实践

1. **数据获取**：优先使用 useRequest + services 封装
2. **用户操作**：使用 callApi + UI反馈配置  
3. **错误处理**：依赖 callApi 的自动错误处理，减少手动处理
4. **成功反馈**：利用 callApi 的 showSuccessToast 和 onSuccess 回调
5. **加载状态**：对于重要操作启用 showLoading

通过这种架构，业务代码更加简洁，用户体验更加一致。