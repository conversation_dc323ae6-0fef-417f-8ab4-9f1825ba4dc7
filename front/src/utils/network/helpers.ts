/**
 * API 调用辅助工具 - 简洁的业务请求处理
 * 
 * 核心功能：
 * 1. 统一处理API调用的loading和toast反馈
 * 2. 简化常见的成功/失败处理场景
 * 3. 支持自定义回调处理
 */

import { showLoading, hideLoading, showToast, showSuccessToast } from '@/utils/ui/feedback';
import { RESPONSE_CODE, type ApiResponse } from '@/constants/response';
import { BusinessError } from './client';

// ====================================================================
// 类型定义
// ====================================================================

export interface CallApiOptions {
  // UI反馈控制
  showLoading?: boolean;
  loadingText?: string;
  showSuccessToast?: boolean;
  successText?: string;
  showErrorToast?: boolean;

  // 成功回调
  onSuccess?: (data: any) => void | Promise<void>;
}

export interface ApiResult<T = any> {
  success: boolean;
  data?: T;
  error?: any;
  message?: string;
}

// ====================================================================
// 核心 API 调用函数
// ====================================================================

/**
 * API调用包装器 - 统一处理请求和UI反馈
 * 
 * @param apiFunction 返回 ApiResponse<T> 的 API 调用函数
 * @param options 配置选项
 */
export async function callApi<T = any>(
  apiFunction: () => Promise<ApiResponse<T>>,
  options: CallApiOptions = {}
): Promise<ApiResult<T>> {
  const {
    showLoading = false,
    loadingText = '',
    showSuccessToast = false,
    successText,
    showErrorToast = true,
    onSuccess,
  } = options;

  // 显示加载状态
  if (showLoading) {
    uni.showLoading({
      title: loadingText,
      mask: true,
    });
  }

  try {
    // 执行 API 调用
    const response = await apiFunction();

    // 根据业务状态码自动处理
    if (response.code === RESPONSE_CODE.SUCCESS) {

      if (showSuccessToast) {
        // 业务成功：自动处理成功逻辑
        const finalSuccessText = successText || response.message || '操作成功';
        uni.showToast({
          title: finalSuccessText,
          icon: 'success',
          duration: 2000,
        });
      }

      // 执行成功回调
      if (onSuccess) {
        await onSuccess(response.data);
      }

      return {
        success: true,
        data: response.data,  // 直接返回业务数据
        message: response.message,
      };
    } else {
      // 业务失败：自动处理错误逻辑
      const errorMessage = response.message || '操作失败';

      if (showErrorToast !== false) {
        showToast(errorMessage);
      }

      return {
        success: false,
        error: response,
        message: errorMessage,
      };
    }

  } catch (error) {
    // 错误处理
    const errorMessage = getErrorMessage(error);

    if (showErrorToast) {
      showToast(errorMessage);
    }

    return {
      success: false,
      error,
      message: errorMessage,
    };

  } finally {
    // 隐藏加载状态
    if (showLoading) {
      hideLoading();
    }
  }
}

// ====================================================================
// 错误处理工具
// ====================================================================

/**
 * 获取错误信息
 * @param error 错误对象
 */
function getErrorMessage(error: any): string {
  if (!error) return '未知错误';

  // BusinessError (业务错误)
  if (error instanceof BusinessError) {
    return error.message;
  }

  // HTTP错误
  if (error.statusCode) {
    return error.message || `网络错误 (${error.statusCode})`;
  }

  // 标准错误对象
  if (error.message) {
    return error.message;
  }

  // 字符串错误
  if (typeof error === 'string') {
    return error;
  }

  return '请求失败，请重试';
}