import { createAlova } from 'alova'
import VueHook from 'alova/vue'
import UniAppAdapter from '@alova/adapter-uniapp'
import { useUserStore } from '@/stores'
import {
    HTTP_STATUS,
    RESPONSE_CODE,
    RESPONSE_MESSAGE,
    type ApiResponse,
} from '@/constants/response'

// 自定义业务错误，用于在响应拦截器中抛出，携带业务错误码和消息
export class BusinessError extends Error {
    public code: number
    public data: any

    constructor(message: string, code: number, data: any = null) {
        super(message)
        this.name = 'BusinessError'
        this.code = code
        this.data = data
    }
}

// 获取基础URL
const baseURL = import.meta.env.VITE_SERVER_BASEURL || 'http://localhost:8080/api'

// 创建 Alova 请求实例
const alovaInstance = createAlova({
    baseURL,
    timeout: 10000,
    statesHook: VueHook,
    ...UniAppAdapter({
        // enableHttp2: import.meta.env.PROD,
    }),
    // 默认缓存5分钟
    // cacheFor: {
    //     GET: 5 * 60 * 1000,
    // },
    cacheFor: null // 全局关闭缓存，必须关闭
    // shareRequest: true,
})

// 刷新 token 的 Method 实例
// 它的元数据 `isRefreshToken` 用于在拦截器中识别，避免循环刷新
const refreshTokenMethod = () => {
    const userStore = useUserStore()
    // 假设刷新 token 需要携带 refreshToken
    // 请根据实际后端接口调整
    return alovaInstance.Post<ApiResponse<{ accessToken: string; refreshToken: string }>>(
        '/auth/refresh', // 刷新token的接口地址
        { refreshToken: userStore.refreshToken },
        { meta: { isRefreshToken: true } },
    )
}

// 请求拦截器
alovaInstance.options.beforeRequest = async (method) => {
    // 1. 对刷新 token 请求直接放行
    if (method.meta?.isRefreshToken) {
        return
    }

    // 2. 如果 token 正在刷新，则等待刷新完成后再发送当前请求
    const refreshingPromise = alovaInstance.l1Cache.get<Promise<any>>('refreshing-token')
    if (refreshingPromise) {
        await refreshingPromise
    }

    // 3. 添加认证头和设备ID
    method.config.headers = {
        'Content-Type': 'application/json',
        ...method.config.headers,
    }

    try {
        const userStore = useUserStore()
        const { accessToken } = userStore
        if (accessToken) {
            method.config.headers.Authorization = `Bearer ${accessToken}`
        }

        const deviceId = uni.getStorageSync('device_id')
        if (deviceId) {
            method.config.headers['X-Device-ID'] = deviceId
        }
    } catch (error) {
        console.warn('获取 token 或设备 ID 失败:', error)
    }
}

// 响应拦截器
alovaInstance.options.responded = {
    // 响应成功拦截器
    onSuccess: (response, method) => {
        // 1. 处理 uniapp 响应体，获取核心数据
        const { data: apiResponse } = response as UniNamespace.RequestSuccessCallbackResult;

        // 2. 业务失败，抛出自定义业务错误，由 onError 处理
        // if (code !== RESPONSE_CODE.SUCCESS) {
        //     throw new BusinessError(message || RESPONSE_MESSAGE.ERROR, code, data);
        // }

        // 3. 统一返回完整的业务响应体
        return apiResponse as ApiResponse<any>;
    },

    // 响应错误拦截器
    onError: async (error, method) => {
        console.error('请求错误:', error, '发生在', method.url)
        const userStore = useUserStore()

        // 1. 如果是刷新 token 请求本身失败，则清除用户信息并跳转登录页
        if (method.meta?.isRefreshToken) {
            userStore.clearUserInfo()
            uni.navigateTo({ url: '/pages/auth/login' })
            throw error
        }

        // 2. 处理 HTTP 401 Unauthorized 错误（Token 过期）
        // 只有 HTTP 错误才有 statusCode，BusinessError 没有
        if (error.statusCode === HTTP_STATUS.UNAUTHORIZED) {
            // 尝试获取缓存中的刷新请求 promise
            let refreshingPromise = alovaInstance.l1Cache.get<Promise<any>>('refreshing-token')

            if (!refreshingPromise) {
                // 如果没有正在刷新的请求，则发起新的刷新请求
                refreshingPromise = refreshTokenMethod().send()
                // 将 promise 存入缓存
                alovaInstance.l1Cache.set('refreshing-token', refreshingPromise)
            }

            try {
                // 等待 token 刷新成功
                const refreshedResult = await refreshingPromise
                // 更新 store 中的 token
                // @ts-ignore
                userStore.setTokens(refreshedResult.accessToken, refreshedResult.refreshToken)
                // 清除缓存的 promise
                alovaInstance.l1Cache.remove('refreshing-token')
                // 使用新的 token 重新发送原始请求
                return method.send()
            } catch (refreshError) {
                // 刷新 token 失败，清除缓存和用户信息，跳转登录
                alovaInstance.l1Cache.remove('refreshing-token')
                userStore.clearUserInfo()
                uni.navigateTo({ url: '/pages/auth/login' })
                throw refreshError
            }
        }

        // 3. 抛出错误，由上层处理UI反馈
        // 这里包含：HTTP错误（如 400, 500）和 BusinessError（业务错误）
        throw error
    },
}

export const request = alovaInstance
export default request 