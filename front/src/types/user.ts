// src/types/user.ts

/**
 * 用户信息
 */
export interface UserInfo {
    id: number;
    uid: string;
    nickname: string;
    real_name?: string;  // 真实姓名 - 用于零工申请等场景
    avatar: string;
    gender: number;
    birthday: string;
    phone: string;
    email: string;
    id_card?: string;  // 身份证号 - 用于身份验证
    isVip?: boolean;
    vipExpiresAt: string;
    isSuperAdmin: boolean;
    posts: number;
    followers: number;
    following: number;
    balance: number;
    createdAt: string;
    updatedAt: string;
    personalVerified: boolean;
    enterpriseVerified: boolean;
    memberLevel: number;
}

/**
 * 用户地址信息
 */
export interface UserAddress {
    id: number;
    name: string;
    address: string;
    detail_address: string;
    full_address: string;
    latitude: number;
    longitude: number;
    navigation_guide: string;
    guide_images: string[];
    guide_videos: string[];
    is_default: boolean;
    last_used_at?: string;
    created_at: string;
    updated_at: string;
}

/**
 * 创建地址请求
 */
export interface CreateAddressRequest {
    name: string;
    address: string;
    detail_address?: string;
    full_address: string;
    latitude: number;
    longitude: number;
    navigation_guide?: string;
    guide_images?: string[];
    guide_videos?: string[];
    is_default?: boolean;
}

/**
 * 更新地址请求
 */
export interface UpdateAddressRequest {
    name?: string;
    address?: string;
    detail_address?: string;
    full_address?: string;
    latitude?: number;
    longitude?: number;
    navigation_guide?: string;
    guide_images?: string[];
    guide_videos?: string[];
    is_default?: boolean;
}
