// src/types/user.ts

/**
 * 用户信息
 */
export interface UserInfo {
    id: number;
    uid: string;
    nickname: string;
    real_name?: string;  // 真实姓名 - 用于零工申请等场景
    avatar: string;
    gender: number;
    birthday: string;
    phone: string;
    email: string;
    id_card?: string;  // 身份证号 - 用于身份验证
    isVip?: boolean;
    vipExpiresAt: string;
    isSuperAdmin: boolean;
    posts: number;
    followers: number;
    following: number;
    balance: number;
    createdAt: string;
    updatedAt: string;
    personalVerified: boolean;
    enterpriseVerified: boolean;
    memberLevel: number;
}

/**
 * 用户地址信息
 */
export interface UserAddress {
    id: number;
    name: string;
    address: string;
    detailAddress: string;
    fullAddress: string;
    latitude: number;
    longitude: number;
    navigationGuide: string;
    guideImages: string[];
    guideVideos: string[];
    isDefault: boolean;
    lastUsedAt?: string;
    createdAt: string;
    updatedAt: string;
}

/**
 * 创建地址请求
 */
export interface CreateAddressRequest {
    name: string;
    address: string;
    detailAddress?: string;
    fullAddress: string;
    latitude: number;
    longitude: number;
    navigationGuide?: string;
    guideImages?: string[];
    guideVideos?: string[];
    isDefault?: boolean;
}

/**
 * 更新地址请求
 */
export interface UpdateAddressRequest {
    name?: string;
    address?: string;
    detailAddress?: string;
    fullAddress?: string;
    latitude?: number;
    longitude?: number;
    navigationGuide?: string;
    guideImages?: string[];
    guideVideos?: string[];
    isDefault?: boolean;
}

/**
 * 地址列表响应
 */
export interface GetAddressListResponse {
    addresses: UserAddress[];
} 