// src/types/common.ts

/**
 * 分页请求参数
 */
export interface PaginatedRequest {
    page: number;
    page_size: number;
}

/**
 * 通用分页响应数据结构
 */
export interface PaginatedResponse<T> {
    list: T[];
    total: number;
    page: number;
    page_size: number;
    total_pages: number;
    has_next?: boolean;
    has_prev?: boolean;
}

/**
 * 上传凭证响应
 */
export interface UploadTokenResponse {
    token: string;
    domain: string;
}

/**
 * 标签组件相关类型定义
 */
export const TAG_VARIANTS = ['default', 'primary', 'success', 'warning', 'danger', 'info'] as const
export type TagVariant = typeof TAG_VARIANTS[number]

export const TAG_SIZES = ['small', 'medium', 'large'] as const  
export type TagSize = typeof TAG_SIZES[number]

export const TAG_SHAPES = ['square', 'round', 'pill'] as const
export type TagShape = typeof TAG_SHAPES[number]

/**
 * 标签选项接口
 */
export interface TagOption {
    /** 标签唯一标识 */
    id?: string | number
    /** 显示文本 */
    text?: string
    /** 显示标签（兼容字段） */
    label?: string
    /** 标签值 */
    value: any
    /** 标签变体 */
    variant?: TagVariant
    /** 是否空心 */
    outlined?: boolean
    /** 是否禁用 */
    disabled?: boolean
    /** 是否可移除 */
    removable?: boolean
    /** 自定义颜色 */
    color?: string
    /** 扩展数据 */
    [key: string]: any
}

/**
 * 标签验证结果
 */
export interface TagValidationResult {
    /** 是否有效 */
    valid: boolean
    /** 错误信息 */
    error?: string
}

/**
 * 标签选择器配置
 */
export interface TagSelectorConfig {
    /** 是否多选 */
    multiple?: boolean
    /** 最大选择数量 */
    max?: number
    /** 最小选择数量 */
    min?: number
    /** 是否允许自定义标签 */
    allowCustom?: boolean
    /** 自定义标签前缀 */
    customTagPrefix?: string
    /** 输入框占位符 */
    placeholder?: string
    /** 自定义验证函数 */
    validateCustomTag?: (label: string) => boolean | string
}

/**
 * Tag 组件属性接口
 */
export interface TagProps {
    /** 标签文本内容 */
    text?: string
    /** 标签变体类型 */
    variant?: TagVariant
    /** 标签尺寸 */
    size?: TagSize
    /** 标签形状 */
    shape?: TagShape
    /** 是否为空心样式 */
    outlined?: boolean
    /** 是否可点击 */
    clickable?: boolean
    /** 是否可移除 */
    removable?: boolean
    /** 是否禁用 */
    disabled?: boolean
    /** 自定义背景颜色 */
    bgColor?: string
    /** 自定义文字颜色 */
    textColor?: string
    /** 自定义边框颜色 */
    borderColor?: string
    /** 显示图标 */
    icon?: string
    /** 图标位置 */
    iconPosition?: 'left' | 'right'
    /** 自定义样式类名 */
    customClass?: string
}

/**
 * TagSelector 组件属性接口
 */
export interface TagSelectorProps {
    /** 绑定的值 */
    modelValue?: any[] | any
    /** 选项数据 */
    options: TagOption[]
    /** 是否多选 */
    multiple?: boolean
    /** 最大选择数量，-1为无限制 */
    max?: number
    /** 最小选择数量 */
    min?: number
    /** 标签间距 */
    gap?: string | number
    /** 容器内边距 */
    padding?: string
    /** 是否禁用所有标签 */
    disabled?: boolean
    /** 文本字段映射 */
    textField?: string
    /** 值字段映射 */
    valueField?: string
    /** 禁用字段映射 */
    disabledField?: string
    /** 自定义样式类名 */
    customClass?: string
    /** 验证函数 */
    validator?: (value: any) => TagValidationResult
}

/**
 * TagSelector 组件事件接口
 */
export interface TagSelectorEvents {
    /** 值变化事件 */
    'update:modelValue': [value: any[] | any]
    /** 选择变化事件 */
    change: [value: any[] | any, option?: TagOption, index?: number]
    /** 达到最大选择数事件 */
    maxReached: [max: number]
    /** 验证失败事件 */
    validationError: [error: string]
} 