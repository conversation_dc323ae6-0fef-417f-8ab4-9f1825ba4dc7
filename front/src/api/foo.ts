/**
 * Common 模块 API 定义 - 命名空间模式
 * 职责：只负责定义与后端接口对应的 alova Method 实例
 * 不包含任何业务逻辑，保持纯粹
 */
import request from '@/utils/network/client'
import type { UploadTokenResponse } from "@/types/common";
import { ApiResponse } from '@/constants/response';

/**
 * 通用功能 API 命名空间类
 * 采用静态方法组织，提供清晰的模块边界
 */
class CommonApi {
    /**
     * 获取七牛云上传凭证
     * @returns alova Method 实例
     */
    static getUploadToken = () => {
        return request.Post<ApiResponse<UploadTokenResponse>>("/common/upload-token");
    }

    /**
     * 文件上传
     * @param file 文件数据
     * @returns 上传结果
     */
    static uploadFile = (file: any) => {
        return request.Post<{ url: string; key: string }>("/common/upload", file);
    }

    /**
     * 获取配置信息
     * @returns 配置信息
     */
    static getConfig = () => {
        return request.Get<any>("/common/config");
    }
}

// 默认导出命名空间类
export default CommonApi

// 向后兼容性导出 (过渡期使用，后续可移除)
export const getUploadToken = CommonApi.getUploadToken
export const uploadFile = CommonApi.uploadFile
export const getConfig = CommonApi.getConfig


