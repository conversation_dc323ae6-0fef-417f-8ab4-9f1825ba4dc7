/**
 * User 模块 API 定义 - 命名空间模式
 * 职责：只负责定义与后端接口对应的 alova Method 实例
 * 不包含任何业务逻辑，保持纯粹
 */
import request from '@/utils/network/client'
import type { ApiResponse } from '@/constants/response'
import type { 
    UserInfo, 
    UserAddress, 
    CreateAddressRequest, 
    UpdateAddressRequest, 
    GetAddressListResponse 
} from '@/types/user'

/**
 * 用户相关 API 命名空间类
 * 采用静态方法组织，提供清晰的模块边界
 */
class UserApi {
    // ====================================================================
    // 用户信息管理
    // ====================================================================

    /**
     * 获取当前登录用户信息
     * @returns 用户信息
     */
    static getProfile = () => {
        return request.Get<ApiResponse<UserInfo>>("/users/profile")
    }

    /**
     * 更新用户信息
     * @param data 更新的用户数据
     * @returns 更新结果
     */
    static updateProfile = (data: Partial<UserInfo>) => {
        return request.Post<{ message: string }>("/users/profile", data)
    }

    // ====================================================================
    // 用户设置
    // ====================================================================

    /**
     * 获取用户设置
     * @returns 用户设置信息
     */
    static getSettings = () => {
        return request.Get<any>("/users/settings")
    }

    /**
     * 更新用户设置
     * @param data 设置数据
     * @returns 更新结果
     */
    static updateSettings = (data: Record<string, any>) => {
        return request.Post<{ message: string }>("/users/settings", data)
    }

    // ====================================================================
    // 用户统计
    // ====================================================================

    /**
     * 获取用户统计信息
     * @returns 统计数据
     */
    static getStats = () => {
        return request.Get<any>("/users/stats")
    }

    /**
     * 获取用户活动记录
     * @param params 查询参数
     * @returns 活动记录列表
     */
    static getActivities = (params: { page: number; page_size: number }) => {
        return request.Get<any>("/users/activities", { params })
    }

    // ====================================================================
    // 用户地址管理
    // ====================================================================

    /**
     * 创建用户地址
     * @param data 地址数据
     * @returns 创建的地址信息
     */
    static createAddress = (data: CreateAddressRequest) => {
        return request.Post<ApiResponse<UserAddress>>("/addresses", data)
    }

    /**
     * 获取用户地址列表
     * @returns 地址列表
     */
    static getAddressList = () => {
        return request.Get<ApiResponse<GetAddressListResponse>>("/addresses")
    }

    /**
     * 获取地址详情
     * @param id 地址ID
     * @returns 地址详情
     */
    static getAddressDetail = (id: number) => {
        return request.Get<ApiResponse<UserAddress>>(`/addresses/${id}`)
    }

    /**
     * 更新用户地址
     * @param id 地址ID
     * @param data 更新数据
     * @returns 更新结果
     */
    static updateAddress = (id: number, data: UpdateAddressRequest) => {
        return request.Put<ApiResponse<{ message: string }>>(`/addresses/${id}`, data)
    }

    /**
     * 删除用户地址
     * @param id 地址ID
     * @returns 删除结果
     */
    static deleteAddress = (id: number) => {
        return request.Delete<ApiResponse<{ message: string }>>(`/addresses/${id}`)
    }

    /**
     * 设置默认地址
     * @param id 地址ID
     * @returns 设置结果
     */
    static setDefaultAddress = (id: number) => {
        return request.Post<ApiResponse<{ message: string }>>(`/addresses/${id}/default`)
    }

    /**
     * 使用地址（更新最后使用时间）
     * @param id 地址ID
     * @returns 使用结果
     */
    static useAddress = (id: number) => {
        return request.Post<ApiResponse<{ message: string }>>(`/addresses/${id}/use`)
    }
}

// ====================================================================
// 导出配置
// ====================================================================

// 默认导出命名空间类
export default UserApi

// 命名导出（可选）
export { UserApi }

// ====================================================================
// 向后兼容性导出 (过渡期使用，后续可移除)
// ====================================================================

export const getUserProfile = UserApi.getProfile
export const updateUserProfile = UserApi.updateProfile
export const getUserSettings = UserApi.getSettings
export const updateUserSettings = UserApi.updateSettings
export const getUserStats = UserApi.getStats
export const getUserActivities = UserApi.getActivities