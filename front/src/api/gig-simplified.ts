/**
 * 零工模块 API - 简化版本
 * 直接函数导出，减少封装复杂度
 */
import request from "@/utils/network/client";
import type {
  Gig,
  CheckApplicationStatusResponse,
  ApplyGigRequest,
  CreateGigApplicationRequest,
  ListGigsRequest,
  GigApplication,
  UpdateApplicationStatusRequest,
  CreateGigRequest,
  MonthlyStatsResponse,
  DailyGigsAndApplicationsResponse,
} from "@/types/gig";
import { PaginatedResponse } from "@/types/common";

// ====================================================================
// CRUD 操作
// ====================================================================

export const createGig = (data: CreateGigRequest) => {
  return request.Post<{ gigId: number }>("/gigs", data);
};

export const updateGig = (gigId: number, data: Partial<CreateGigRequest>) => {
  return request.Put<{ message: string }>(`/gigs/${gigId}`, data);
};

export const deleteGig = (gigId: number) => {
  return request.Delete(`/gigs/${gigId}`);
};

export const getGigDetail = (id: number) => {
  return request.Get<Gig>(`/gigs/${id}`, {
    meta: { dataOnly: true }
  });
};

// ====================================================================
// 列表查询
// ====================================================================

export const getGigList = (params: ListGigsRequest) => {
  return request.Get<PaginatedResponse<Gig>>("/gigs", { 
    params,
    meta: { dataOnly: true }
  });
};

export const getMyGigs = (params: { page?: number; page_size?: number }) => {
  return request.Get<PaginatedResponse<Gig>>("/gigs/manage", {
    params,
    meta: { dataOnly: true }
  });
};

// ====================================================================
// 申请相关
// ====================================================================

export const applyForGig = (payload: ApplyGigRequest) => {
  return request.Post<{ message: string; application_id: number }>(
    "/gigs/applications",
    payload
  );
};

export const deleteApplication = (applicationId: number) => {
  return request.Delete(`/gigs/applications/${applicationId}`);
};

export const getMyApplications = (params?: {
  page?: number;
  page_size?: number;
}) => {
  return request.Get<PaginatedResponse<GigApplication>>(
    "/gigs/applications/my",
    { 
      params,
      meta: { dataOnly: true }
    }
  );
};

export const getGigApplications = (
  gigId: number,
  params?: { page?: number; page_size?: number }
) => {
  return request.Get<PaginatedResponse<GigApplication>>(
    `/gigs/${gigId}/applications`,
    { 
      params,
      meta: { dataOnly: true }
    }
  );
};

export const checkApplicationStatus = (gigId: number) => {
  return request.Get<CheckApplicationStatusResponse>(
    `/gigs/${gigId}/application-status`,
    {
      meta: { dataOnly: true }
    }
  );
};

export const reviewApplication = (params: UpdateApplicationStatusRequest) => {
  return request.Post("/gigs/applications/review", params);
};

// ====================================================================
// 统计功能
// ====================================================================

export const getMonthlyStats = (year: number, month: number) => {
  return request.Get<MonthlyStatsResponse>(
    `/gigs/calendar/monthly?year=${year}&month=${month}`,
    {
      meta: { dataOnly: true }
    }
  );
};

export const getDailyGigs = (date: string) => {
  return request.Get<DailyGigsAndApplicationsResponse>(
    `/gigs/calendar/daily?date=${date}`,
    {
      meta: { dataOnly: true }
    }
  );
};