<template>
  <BottomPopup
    v-model:show="show"
    title="性别年龄"
    padding="32rpx 32rpx 0"
    @close="handleClose"
  >
    <view class="h-40vh flex-y">
      <!-- 性别选择 -->
      <view class="section">
        <view class="section-title">
          <text class="title-text">性别要求</text>
        </view>
        <view class="gender-cards">
          <view
            v-for="option in genderOptions"
            :key="option.code"
            class="gender-card"
            :class="{ active: selectedGender === option.code }"
            @click="selectGender(option.code)"
          >
            <text class="gender-label">{{ option.label }}</text>
            <view class="gender-check" v-if="selectedGender === option.code">
              <text class="check-mark">✓</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 年龄选择 -->
      <view class="section">
        <view class="section-title">
          <text class="title-text">年龄范围</text>
          <view class="age-display">
            <text class="age-text">{{ ageDisplayText }}</text>
          </view>
        </view>

        <!-- 年龄范围滑块 -->
        <view class="age-slider-container">
          <tui-slider
            :min="MIN_AGE"
            :max="MAX_AGE"
            :step="1"
            :value="currentMinAge"
            :endValue="currentMaxAge"
            :section="true"
            :width="330"
            :height="8"
            showValue
            backgroundColor="#f0f0f1"
            activeColor="#ff6d00"
            blockColor="var(--primary)"
            @change="onAgeRangeChange"
          />

          <!-- 年龄刻度标识 -->
          <view class="age-marks">
            <text class="age-mark">16</text>
            <text class="age-mark">20</text>
            <text class="age-mark">30</text>
            <text class="age-mark">40</text>
            <text class="age-mark">50</text>
            <text class="age-mark">不限</text>
          </view>
        </view>
      </view>

      <!-- 统一底部操作栏 -->
      <BottomActionBar>
        <button class="confirm-btn" @click="handleConfirm">
          确定
        </button>
      </BottomActionBar>
    </view>
  </BottomPopup>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { genderOptions } from "@/constants/standards";
import tuiSlider from "@/components/thorui/tui-slider/tui-slider.vue";
import BottomPopup from "@/components/common/BottomPopup.vue";
import BottomActionBar from "@/components/common/BottomActionBar.vue";

// 常量定义
const MIN_AGE = 16;
const MAX_AGE = 65;

// 组件属性
interface Props {
  show: boolean;
  gender?: number;
  minAge?: number;
  maxAge?: number;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  gender: 0, // 默认不限
  minAge: 16,
  maxAge: 65,
});

// 事件定义
const emit = defineEmits<{
  "update:show": [value: boolean];
  confirm: [gender: number, minAge: number, maxAge: number];
}>();

// 响应式数据
const selectedGender = ref(0);
const currentMinAge = ref(MIN_AGE);
const currentMaxAge = ref(MAX_AGE);

// 计算属性
const ageDisplayText = computed(() => {
  const minText = currentMinAge.value === MIN_AGE ? "16" : currentMinAge.value.toString();
  const maxText = currentMaxAge.value === MAX_AGE ? "不限" : currentMaxAge.value.toString();

  if (currentMinAge.value === MIN_AGE && currentMaxAge.value === MAX_AGE) {
    return "16岁~不限";
  }

  return `${minText}岁~${maxText === "不限" ? "不限" : maxText + "岁"}`;
});

// 方法
const selectGender = (genderCode: number) => {
  selectedGender.value = genderCode;
};

const onAgeRangeChange = (e: any) => {
  if (e && typeof e.value === 'number' && typeof e.endValue === 'number') {
    currentMinAge.value = e.value;
    currentMaxAge.value = e.endValue;
  }
};

const handleClose = () => {
  emit("update:show", false);
};

const handleConfirm = () => {
  console.log("确认选择:", { 
    gender: selectedGender.value, 
    minAge: currentMinAge.value, 
    maxAge: currentMaxAge.value 
  });
  emit("confirm", selectedGender.value, currentMinAge.value, currentMaxAge.value);
  handleClose();
};

// 初始化组件数据
const initializeData = () => {
  selectedGender.value = props.gender || 0;
  currentMinAge.value = props.minAge || MIN_AGE;
  currentMaxAge.value = props.maxAge || MAX_AGE;
  console.log("初始化数据:", {
    gender: selectedGender.value,
    minAge: currentMinAge.value,
    maxAge: currentMaxAge.value,
  });
};

// 监听props变化，并初始化数据
watch(
  () => props.show,
  (newVal) => {
    if (newVal) {
      initializeData();
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.section {
  margin-bottom: 48rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .title-text {
      font-size: 30rpx;
      font-weight: 500;
      color: var(--text-info);
      position: relative;
    }

    .age-display {
      .age-text {
        font-size: 28rpx;
        font-weight: 600;
        color: var(--primary);
        background: linear-gradient(
          135deg,
          var(--primary-100) 0%,
          var(--primary-50) 100%
        );
        padding: 12rpx 20rpx;
        border-radius: 20rpx;
        border: 2rpx solid var(--primary-200);
        box-shadow: 0 2rpx 8rpx rgba(255, 109, 0, 0.15);
        position: relative;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            135deg,
            var(--primary-200) 0%,
            var(--primary-100) 100%
          );
          opacity: 0;
          transition: opacity 0.3s ease;
        }
      }
    }
  }
}

// 性别卡片样式
.gender-cards {
  display: flex;
  gap: 16rpx;
  justify-content: space-between;
}

.gender-card {
  flex: 1;
  background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
  border: 2rpx solid var(--border-light);
  border-radius: 20rpx;
  padding: 24rpx 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      var(--primary-50) 0%,
      var(--primary-25) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &.active {
    border-color: var(--primary);
    background: linear-gradient(135deg, var(--primary-50) 0%, #fff9ec 100%);
    box-shadow: 0 8rpx 24rpx rgba(255, 109, 0, 0.2);
    transform: translateY(-2rpx);

    &::before {
      opacity: 1;
    }

    .gender-label {
      color: var(--primary);
      font-weight: 600;
    }
  }

  .gender-label {
    font-size: 28rpx;
    color: var(--text-base);
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    font-weight: 500;
  }

  .gender-check {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    width: 32rpx;
    height: 32rpx;
    background: linear-gradient(
      135deg,
      var(--primary-600) 0%,
      var(--primary-400) 100%
    );
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2rpx 8rpx rgba(255, 109, 0, 0.3);
    z-index: 2;
    animation: checkIn 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    .check-mark {
      color: #ffffff;
      font-size: 20rpx;
      font-weight: bold;
      line-height: 1;
    }
  }
}

@keyframes checkIn {
  0% {
    transform: scale(0) rotate(-180deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(-90deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

// 年龄滑块容器样式
.age-slider-container {
  position: relative;
  padding: 32rpx 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 24rpx;
  margin-top: 16rpx;
  border: 1rpx solid var(--border-light);

  .age-marks {
    width: 330px;
    display: flex;
    justify-content: space-between;
    margin-top: 32rpx;
    padding: 0 24rpx;

    .age-mark {
      font-size: 24rpx;
      color: var(--text-info);
      text-align: center;
      flex-shrink: 0;
      font-weight: 500;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: -20rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 2rpx;
        height: 12rpx;
        background: var(--border-color);
      }
    }
  }
}

// 确认按钮样式
.confirm-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-400) 100%);
  border-radius: 44rpx;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:active {
    transform: scale(0.98);
    opacity: 0.8;
  }
}
</style>
