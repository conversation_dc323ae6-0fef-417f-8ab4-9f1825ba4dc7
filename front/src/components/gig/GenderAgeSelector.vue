<template>
  <tui-bottom-popup
    :show="show"
    @close="handleClose"
    :mask="true"
    :z-index="1001"
    :mask-z-index="1000"
    :isSafeArea="false"
  >
    <view class="gender-age-selector">
      <!-- 弹框头部 -->
      <view class="popup-header">
        <view class="header-content">
          <text class="popup-title">性别年龄</text>
          <view class="close-btn" @click="handleClose">
            <text class="close-icon">×</text>
          </view>
        </view>
        <view class="header-divider"></view>
      </view>

      <!-- 内容区域 -->
      <view class="popup-content">
        <!-- 性别选择 -->
        <view class="section">
          <view class="section-title">
            <text class="title-text">性别要求</text>
          </view>
          <view class="gender-buttons">
            <view
              v-for="option in genderOptions"
              :key="option.code"
              class="gender-btn"
              :class="{ active: selectedGender === option.code }"
              @click="selectGender(option.code)"
            >
              <text class="gender-btn-text">{{ option.label }}</text>
            </view>
          </view>
        </view>

        <!-- 年龄选择 -->
        <view class="section">
          <view class="section-title">
            <text class="title-text">年龄范围</text>
            <view class="age-display">
              <text class="age-text">{{ ageDisplayText }}</text>
            </view>
          </view>

          <!-- 年龄范围滑块 -->
          <view class="age-slider-container">
            <tui-slider
              :min="MIN_AGE"
              :max="MAX_AGE"
              :step="1"
              :value="currentMinAge"
              :endValue="currentMaxAge"
              :section="true"
              :width="300"
              :height="8"
              showValue
              activeColor="#FF6D00"
              backgroundColor="#F5F5F5"
              :blockWidth="24"
              :blockHeight="24"
              @change="onAgeRangeChange"
            />

            <!-- 年龄刻度标识 -->
            <view class="age-marks">
              <text class="age-mark">16</text>
              <text class="age-mark">20</text>
              <text class="age-mark">30</text>
              <text class="age-mark">40</text>
              <text class="age-mark">50</text>
              <text class="age-mark">不限</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 确认按钮 -->
      <view class="confirm-section">
        <view class="confirm-btn" @click="handleConfirm">
          <text class="confirm-text">确定</text>
        </view>
      </view>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { genderOptions } from "@/constants/standards";
import tuiSlider from "@/components/thorui/tui-slider/tui-slider.vue"

// 类型定义
interface SliderChangeEvent {
  detail: {
    value: number;
    endValue: number;
  };
}

// 常量定义
const MIN_AGE = 16;
const MAX_AGE = 65;

// 组件属性
interface Props {
  show: boolean;
  gender?: number;
  minAge?: number;
  maxAge?: number;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  gender: 0, // 默认不限
  minAge: 16,
  maxAge: 65,
});

// 事件定义
const emit = defineEmits<{
  close: [];
  confirm: [gender: number, minAge: number, maxAge: number];
}>();

// 响应式数据
const selectedGender = ref(0);
const currentMinAge = ref(MIN_AGE);
const currentMaxAge = ref(MAX_AGE);

// 初始化标志，避免初始化时触发slider事件
const isInitialized = ref(false);

// 计算属性
const ageDisplayText = computed(() => {
  const minText =
    currentMinAge.value === MIN_AGE ? "16" : currentMinAge.value.toString();
  const maxText =
    currentMaxAge.value === MAX_AGE ? "不限" : currentMaxAge.value.toString();

  if (currentMinAge.value === MIN_AGE && currentMaxAge.value === MAX_AGE) {
    return "16岁~不限";
  }

  return `${minText}岁~${maxText === "不限" ? "不限" : maxText + "岁"}`;
});

// 方法
const selectGender = (genderCode: number) => {
  selectedGender.value = genderCode;
};

const onAgeRangeChange = (e: SliderChangeEvent) => {
  // 避免初始化时的事件触发
  if (!isInitialized.value) {
    return;
  }

  // 验证事件数据的有效性
  if (
    e?.detail &&
    typeof e.detail.value === "number" &&
    typeof e.detail.endValue === "number"
  ) {
    currentMinAge.value = Math.max(MIN_AGE, Math.min(MAX_AGE, e.detail.value));
    currentMaxAge.value = Math.max(
      MIN_AGE,
      Math.min(MAX_AGE, e.detail.endValue)
    );
  } else {
    console.warn("Invalid slider event data:", e);
  }
};

const handleClose = () => {
  emit("close");
};

const handleConfirm = () => {
  emit(
    "confirm",
    selectedGender.value,
    currentMinAge.value,
    currentMaxAge.value
  );
  handleClose();
};

// 初始化组件数据
const initializeData = () => {
  selectedGender.value = props.gender || 0;
  currentMinAge.value = props.minAge || MIN_AGE;
  currentMaxAge.value = props.maxAge || MAX_AGE;
};

// 组件挂载时初始化
onMounted(() => {
  initializeData();
  // 延迟设置初始化标志，确保slider组件已经完成渲染
  setTimeout(() => {
    isInitialized.value = true;
  }, 100);
});

// 监听props变化（移除immediate选项避免初始化时触发）
watch(
  [() => props.gender, () => props.minAge, () => props.maxAge],
  ([newGender, newMinAge, newMaxAge]) => {
    // 临时禁用初始化标志避免触发slider事件
    const wasInitialized = isInitialized.value;
    isInitialized.value = false;

    selectedGender.value = newGender || 0;
    currentMinAge.value = newMinAge || MIN_AGE;
    currentMaxAge.value = newMaxAge || MAX_AGE;

    // 恢复初始化标志
    setTimeout(() => {
      isInitialized.value = wasInitialized;
    }, 50);
  }
);
</script>

<style lang="scss" scoped>
.gender-age-selector {
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 32rpx 32rpx 0 0;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.popup-header {
  background: #ffffff;
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40rpx 32rpx 24rpx;

    .popup-title {
      font-size: 36rpx;
      font-weight: 600;
      color: var(--text-primary);
    }

    .close-btn {
      width: 56rpx;
      height: 56rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--bg-secondary);
      border-radius: 50%;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.9);
        background: var(--bg-tertiary);
      }

      .close-icon {
        font-size: 32rpx;
        color: var(--text-secondary);
        line-height: 1;
      }
    }
  }

  .header-divider {
    height: 1rpx;
    background: linear-gradient(90deg, transparent 0%, var(--border-light) 20%, var(--border-light) 80%, transparent 100%);
    margin: 0 32rpx;
  }
}

.popup-content {
  padding: 32rpx;
}

.section {
  margin-bottom: 48rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .title-text {
      font-size: 30rpx;
      font-weight: 600;
      color: var(--text-primary);
    }

    .age-display {
      .age-text {
        font-size: 28rpx;
        font-weight: 600;
        color: var(--primary);
        background: linear-gradient(135deg, var(--primary-100) 0%, var(--primary-50) 100%);
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        border: 1rpx solid var(--primary-200);
      }
    }
  }

  .gender-buttons {
    display: flex;
    gap: 16rpx;

    .gender-btn {
      flex: 1;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--bg-secondary);
      border: 2rpx solid var(--border-light);
      border-radius: 20rpx;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-400) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &.active {
        background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-400) 100%);
        border-color: var(--primary);
        box-shadow: 0 4rpx 16rpx rgba(255, 109, 0, 0.3);
        transform: translateY(-2rpx);

        .gender-btn-text {
          font-weight: 600;
          color: #ffffff;
          position: relative;
          z-index: 1;
        }
      }

      &:active {
        transform: scale(0.98);
      }

      .gender-btn-text {
        font-size: 30rpx;
        color: var(--text-secondary);
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;
      }
    }
  }
}

.age-slider-container {
  position: relative;
  padding: 24rpx 0;

  .age-marks {
    display: flex;
    justify-content: space-between;
    margin-top: 32rpx;
    padding: 0 12rpx;

    .age-mark {
      font-size: 24rpx;
      color: var(--text-tertiary);
      text-align: center;
      flex-shrink: 0;
      font-weight: 500;
    }
  }
}

.confirm-section {
  padding: 24rpx 32rpx 40rpx;
  background: #ffffff;

  .confirm-btn {
    width: 100%;
    height: 96rpx;
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-400) 100%);
    border-radius: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 24rpx rgba(255, 109, 0, 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, var(--primary-700) 0%, var(--primary-500) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:active {
      transform: scale(0.98);
      box-shadow: 0 4rpx 16rpx rgba(255, 109, 0, 0.6);

      &::before {
        opacity: 1;
      }
    }

    .confirm-text {
      font-size: 32rpx;
      color: #ffffff;
      font-weight: 600;
      position: relative;
      z-index: 1;
    }
  }
}
</style>
