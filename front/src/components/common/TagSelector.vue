<template>
  <view :class="containerClass" :style="containerStyle">
    <view
      v-for="(option, index) in normalizedOptions"
      :key="index"
      :class="getTagClass(option)"
      :style="getTagStyle(option)"
      @tap.stop="handleTagClick(option, index)"
    >
      {{ getOptionText(option) }}
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";

// ==================== 类型定义 ====================
interface TagOption {
  /** 显示文本 */
  label: string;
  /** 选项值 */
  value: any;
  /** 是否禁用 */
  disabled?: boolean;
}

interface Props {
  /** 绑定值 */
  modelValue?: any[] | any;
  /** 选项数据，支持字符串数组或对象数组 */
  options: (string | TagOption)[];
  /** 是否多选 */
  multiple?: boolean;
  /** 最大选择数量，-1为无限制 */
  max?: number;
  /** 是否禁用 */
  disabled?: boolean;

  // ==================== 尺寸控制 ====================
  /** 标签项宽度（rpx），0为自适应 */
  width?: number | string;
  /** 标签项内边距 */
  padding?: string;
  /** 字体大小 */
  fontSize?: number | string;
  /** 标签间距 */
  gap?: string;

  // ==================== 颜色控制 ====================
  /** 默认文字颜色 */
  color?: string;
  /** 选中文字颜色 */
  activeColor?: string;
  /** 默认背景色 */
  background?: string;
  /** 选中背景色 */
  activeBgColor?: string;
  /** 选中边框颜色 */
  borderColor?: string;
  /** 默认边框颜色 */
  defaultBorderColor?: string;

  // ==================== 形状控制 ====================
  /** 圆角大小 */
  radius?: string;
  /** 边框宽度 */
  borderWidth?: string;

  /** 自定义样式类 */
  customClass?: string;
}

interface Emits {
  /** 值变化事件 */
  "update:modelValue": [value: any];
  /** 选择变化事件 */
  change: [value: any, option: TagOption, index: number];
}

// ==================== 组件定义 ====================
const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  options: () => [],
  multiple: false,
  max: -1,
  disabled: false,

  // 尺寸默认值
  width: 0,
  padding: "12rpx 20rpx",
  fontSize: "26rpx",
  gap: "16rpx",

  // 颜色默认值
  color: "var(--text-secondary)",
  activeColor: "var(--primary)",
  background: "var(--bg-tag)",
  activeBgColor: "var(--bg-primary-light)",
  borderColor: "var(--primary)",
  defaultBorderColor: "transparent",

  // 形状默认值
  radius: "var(--radius)",
  borderWidth: "2rpx",
});

const emit = defineEmits<Emits>();

// ==================== 状态管理 ====================
const selectedValues = ref<any[]>([]);

// 初始化选中值
const initSelectedValues = () => {
  if (props.multiple) {
    selectedValues.value = Array.isArray(props.modelValue)
      ? [...props.modelValue]
      : [];
  } else {
    selectedValues.value =
      props.modelValue !== undefined ? [props.modelValue] : [];
  }
};

// 监听 modelValue 变化
watch(() => props.modelValue, initSelectedValues, { immediate: true });

// ==================== 计算属性 ====================
// 规范化选项数据
const normalizedOptions = computed((): TagOption[] => {
  return props.options.map((option, index) => {
    if (typeof option === "string") {
      return {
        label: option,
        value: option,
        disabled: false,
      };
    }
    return {
      label: option.label || String(option.value),
      value: option.value,
      disabled: option.disabled || false,
    };
  });
});

// 容器样式类
const containerClass = computed(() => {
  const classes = ["tag-selector"];
  if (props.customClass) {
    classes.push(props.customClass);
  }
  return classes.join(" ");
});

// 容器样式
const containerStyle = computed(() => {
  return {
    gap: props.gap,
  };
});

const getOptionText = (option: TagOption): string => {
  return option.label;
};

const isOptionSelected = (option: TagOption): boolean => {
  return selectedValues.value.includes(option.value);
};

// 获取标签样式
const getTagStyle = (option: TagOption) => {
  const isSelected = isOptionSelected(option);
  const isDisabled = props.disabled || option.disabled;

  return {
    width:
      props.width === 0
        ? "auto"
        : typeof props.width === "number"
        ? `${props.width}rpx`
        : props.width,
    padding: props.padding,
    fontSize:
      typeof props.fontSize === "number"
        ? `${props.fontSize}rpx`
        : props.fontSize,
    color: isSelected ? props.activeColor : props.color,
    backgroundColor: isSelected ? props.activeBgColor : props.background,
    borderColor: isSelected ? props.borderColor : props.defaultBorderColor,
    borderRadius: props.radius,
    borderWidth: props.borderWidth,
    borderStyle: "solid",
    opacity: isDisabled ? "0.5" : "1",
  };
};

const getTagClass = (option: TagOption): string => {
  const classes = ["tag-selector-item"];

  // 禁用样式
  if (props.disabled || option.disabled) {
    classes.push("tag-selector-item--disabled");
  }

  return classes.join(" ");
};

// ==================== 事件处理 ====================
const handleTagClick = (option: TagOption, index: number) => {
  if (props.disabled || option.disabled) return;

  let newValues = [...selectedValues.value];

  if (props.multiple) {
    // 多选模式
    const valueIndex = newValues.indexOf(option.value);
    if (valueIndex > -1) {
      newValues.splice(valueIndex, 1);
    } else {
      // 检查最大选择数限制
      if (props.max > 0 && newValues.length >= props.max) {
        uni.showToast({
          title: `最多只能选择 ${props.max} 个选项`,
          icon: "none",
        });
        return;
      }
      newValues.push(option.value);
    }
  } else {
    // 单选模式
    newValues = newValues[0] === option.value ? [] : [option.value];
  }

  selectedValues.value = newValues;

  const emitValue = props.multiple ? newValues : newValues[0] ?? undefined;
  emit("update:modelValue", emitValue);
  emit("change", emitValue, option, index);
};

// ==================== 暴露方法 ====================
defineExpose({
  /** 清空选择 */
  clear: () => {
    selectedValues.value = [];
    const emitValue = props.multiple ? [] : undefined;
    emit("update:modelValue", emitValue);
  },
  /** 选择所有（仅多选模式） */
  selectAll: () => {
    if (!props.multiple) return;

    const allValues = normalizedOptions.value
      .filter((opt) => !opt.disabled)
      .map((opt) => opt.value);

    selectedValues.value = allValues;
    emit("update:modelValue", allValues);
  },
  /** 获取当前选中值 */
  getSelectedValues: () => selectedValues.value,
});
</script>

<style lang="scss" scoped>
.tag-selector {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}

.tag-selector-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
  line-height: 1.4;
  user-select: none;
  transition: all 0.2s ease;
  white-space: nowrap;
  box-sizing: border-box;
}
</style>
