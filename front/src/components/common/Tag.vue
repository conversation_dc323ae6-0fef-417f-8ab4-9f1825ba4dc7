<template>
  <view 
    :class="tagClass"
    :style="tagStyle"
    @tap="handleClick"
  >
    <!-- 左侧图标 -->
    <text 
      v-if="icon && iconPosition === 'left'"
      :class="iconClass"
      :style="iconStyle"
    ></text>
    
    <!-- 标签内容 -->
    <text class="tag-text" :style="textStyle">
      <slot>{{ text || '' }}</slot>
    </text>
    
    <!-- 右侧图标 -->
    <text 
      v-if="icon && iconPosition === 'right'"
      :class="iconClass"  
      :style="iconStyle"
    ></text>
    
    <!-- 移除按钮 -->
    <text 
      v-if="removable && !disabled"
      class="tag-remove i-solar-close-circle-bold"
      :style="removeIconStyle"
      @tap.stop="handleRemove"
    ></text>
  </view>
</template>

<script setup lang="ts">
import { computed, type CSSProperties } from 'vue'
import type { TagProps, TagVariant, TagSize, TagShape } from '@/types/common'

// 组件属性定义
const props = withDefaults(defineProps<TagProps>(), {
  variant: 'default',
  size: 'medium',
  shape: 'square',
  outlined: false,
  clickable: false,
  removable: false,
  disabled: false,
  iconPosition: 'left'
})

// 事件定义
const emit = defineEmits<{
  click: []
  remove: []
}>()

// 简化的尺寸配置
const SIZE_CONFIG: Record<TagSize, { fontSize: string; padding: string; iconSize: string }> = {
  small: { fontSize: '24rpx', padding: '6rpx 12rpx', iconSize: '24rpx' },
  medium: { fontSize: '28rpx', padding: '8rpx 16rpx', iconSize: '28rpx' },
  large: { fontSize: '32rpx', padding: '12rpx 20rpx', iconSize: '32rpx' }
}

// 形状配置
const SHAPE_CONFIG: Record<TagShape, string> = {
  square: 'var(--radius)',
  round: 'var(--radius-2)', 
  pill: '50rpx'
}

// 变体颜色配置
const VARIANT_CONFIG: Record<TagVariant, { bg: string; text: string; border: string }> = {
  default: { 
    bg: 'var(--bg-tag)', 
    text: 'var(--text-secondary)', 
    border: 'var(--border-color)' 
  },
  primary: { 
    bg: 'var(--bg-primary-light)', 
    text: 'var(--primary)', 
    border: 'var(--primary)' 
  },
  success: { 
    bg: 'var(--bg-success-light)', 
    text: 'var(--text-green)', 
    border: 'var(--text-green)' 
  },
  warning: { 
    bg: 'var(--bg-warning-light)', 
    text: 'var(--text-yellow)', 
    border: 'var(--text-yellow)' 
  },
  danger: { 
    bg: 'var(--bg-danger-light)', 
    text: 'var(--text-red)', 
    border: 'var(--text-red)' 
  },
  info: { 
    bg: 'var(--bg-info-light)', 
    text: 'var(--text-blue)', 
    border: 'var(--text-blue)' 
  }
}

// 标签样式类
const tagClass = computed(() => {
  return [
    'tag',
    props.clickable && 'tag-clickable',
    props.disabled && 'tag-disabled',
    props.customClass
  ].filter(Boolean).join(' ')
})

// 标签样式
const tagStyle = computed((): CSSProperties => {
  const sizeConfig = SIZE_CONFIG[props.size]
  const shapeRadius = SHAPE_CONFIG[props.shape]
  const variantColors = VARIANT_CONFIG[props.variant]
  
  return {
    fontSize: sizeConfig.fontSize,
    padding: sizeConfig.padding,
    borderRadius: shapeRadius,
    backgroundColor: props.bgColor || (props.outlined ? 'transparent' : variantColors.bg),
    color: props.textColor || variantColors.text,
    border: props.outlined ? `1rpx solid ${props.borderColor || variantColors.border}` : 'none',
    opacity: props.disabled ? '0.5' : '1'
  }
})

// 文本样式
const textStyle = computed((): CSSProperties => {
  return {
    color: props.textColor || VARIANT_CONFIG[props.variant].text
  }
})

// 图标样式类
const iconClass = computed(() => {
  const classes = ['tag-icon']
  if (props.icon) {
    classes.push(props.icon)
  }
  return classes.join(' ')
})

// 图标样式
const iconStyle = computed((): CSSProperties => {
  const sizeConfig = SIZE_CONFIG[props.size]
  return {
    fontSize: sizeConfig.iconSize,
    color: props.textColor || VARIANT_CONFIG[props.variant].text,
    marginRight: props.iconPosition === 'left' ? '6rpx' : '0',
    marginLeft: props.iconPosition === 'right' ? '6rpx' : '0'
  }
})

// 移除图标样式
const removeIconStyle = computed((): CSSProperties => {
  const sizeConfig = SIZE_CONFIG[props.size]
  return {
    fontSize: sizeConfig.iconSize,
    color: props.textColor || VARIANT_CONFIG[props.variant].text,
    marginLeft: '6rpx',
    opacity: '0.7'
  }
})

// 处理点击事件
const handleClick = () => {
  if (props.disabled) return
  if (props.clickable) {
    emit('click')
  }
}

// 处理移除事件
const handleRemove = () => {
  if (props.disabled) return
  emit('remove')
}
</script>

<style lang="scss" scoped>
.tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-sizing: border-box;
  vertical-align: middle;
  line-height: 1.4;
  transition: all 0.2s ease;
  margin-right: var(--spacing);
  margin-bottom: var(--spacing);
}

.tag-clickable {
  cursor: pointer;
  user-select: none;
  
  &:active {
    transform: scale(0.95);
    opacity: 0.8;
  }
}

.tag-disabled {
  cursor: not-allowed;
  user-select: none;
  
  .tag-remove {
    display: none;
  }
}

.tag-text {
  flex: 1;
  text-align: center;
  word-break: break-all;
  line-height: 1.4;
}

.tag-icon {
  display: inline-block;
  vertical-align: middle;
}

.tag-remove {
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
  transition: opacity 0.2s ease;
  
  &:hover {
    opacity: 1;
  }
}
</style>