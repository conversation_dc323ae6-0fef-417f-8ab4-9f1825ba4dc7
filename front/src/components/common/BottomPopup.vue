<template>
  <tui-bottom-popup
    :show="show"
    :maskClosable="maskClosable"
    :backgroundColor="bgColor"
    :radius="radius"
    :zIndex="zIndex"
    :isSafeArea="false"
    @open="handleOpen"
    @close="handleClose"
  >
    <view class="popup-container">
      <!-- Header: Can be replaced by a slot -->
      <slot name="header">
        <view class="popup-header" v-if="title || showCloseBtn">
          <view class="header-left"></view>
          <text class="popup-title">{{ title }}</text>
          <view class="header-right">
            <view
              v-if="showCloseBtn"
              class="close-btn center interactive-scale"
              @click="handleClose"
            >
              <text class="i-carbon-close text-40rpx text-info"></text>
            </view>
          </view>
        </view>
      </slot>

      <!-- Content -->
      <view class="popup-content" :style="{ padding: padding }">
        <slot></slot>
      </view>
      <u-safe-bottom></u-safe-bottom>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
import { withDefaults, defineProps, defineEmits } from "vue";

/**
 * @description 底部弹窗组件，基于 tui-bottom-popup 封装，提供了标准化的头部和更简洁的 API。
 * @property {Boolean} show v-model:show 控制弹窗显示与隐藏
 * @property {String} title 弹窗标题
 * @property {Boolean} showCloseBtn (true) 是否显示关闭按钮
 * @property {Boolean} maskClosable (true) 点击遮罩是否可以关闭
 * @property {String} padding ('32rpx') 内容区域内边距
 * @property {String} bgColor ('var(--bg-card)') 背景颜色
 * @property {String} radius ('24rpx') 顶部圆角
 * @property {Number} zIndex (1000) z-index
 * @event {Function} open 弹窗打开时触发
 * @event {Function} close 弹窗关闭时触发
 */

interface BottomPopupProps {
  /**
   * v-model: 控制弹窗的显示与隐藏
   */
  show: boolean;
  /**
   * 弹窗头部的标题
   */
  title?: string;
  /**
   * 是否显示头部右侧的关闭按钮
   * @default true
   */
  showCloseBtn?: boolean;
  /**
   * 点击遮罩层是否可以关闭弹窗
   * @default true
   */
  maskClosable?: boolean;
  /**
   * 内容区域的内边距
   * @default '32rpx'
   */
  padding?: string;
  /**
   * 弹窗的背景色
   * @default 'var(--bg-card)'
   */
  bgColor?: string;
  /**
   * 弹窗的顶部圆角
   * @default '24rpx'
   */
  radius?: string;
  /**
   * 弹窗的 z-index 层级
   * @default 1000
   */
  zIndex?: number;
}

const props = withDefaults(defineProps<BottomPopupProps>(), {
  show: false,
  title: "",
  showCloseBtn: true,
  maskClosable: true,
  padding: "32rpx",
  bgColor: "var(--bg-card)",
  radius: "24rpx",
  zIndex: 1000,
});

const emit = defineEmits<{
  (e: "update:show", value: boolean): void;
  (e: "open"): void;
  (e: "close"): void;
}>();

const handleOpen = () => {
  emit("open");
};

const handleClose = () => {
  emit("update:show", false);
  emit("close");
};
</script>

<style lang="scss" scoped>
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100rpx;
  border-bottom: 1rpx solid var(--border-light);
  padding: 0 32rpx;
}

.header-left,
.header-right {
  flex: 0 0 60rpx; /* Adjust width as needed */
  display: flex;
  align-items: center;
}

.header-right {
  justify-content: flex-end;
}

.popup-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-base);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.close-btn {
  width: 52rpx;
  height: 52rpx;
  border-radius: 50%;
  background-color: var(--bg-search);
}
</style>
