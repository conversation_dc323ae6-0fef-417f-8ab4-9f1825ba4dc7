<template>
  <view class="stepper-wrapper" :class="[disabled && 'opacity-50']">
    <view
      class="stepper-btn minus-btn"
      :class="{ disabled: isMinDisabled }"
      @click="decrease"
    >
      <view class="i-carbon:subtract-large text-20px" />
    </view>
    <view class="stepper-input-container">
      <input
        v-model="displayValue"
        type="text"
        inputmode="decimal"
        class="stepper-input"
        :placeholder="placeholder"
        :disabled="disabled"
        @input="handleInput"
        @blur="handleBlur"
      />
      <text v-if="unit" class="stepper-unit">{{ unit }}</text>
    </view>
    <view
      class="stepper-btn plus-btn"
      :class="{ disabled: isMaxDisabled }"
      @click="increase"
    >
      <view class="i-carbon:add-large text-20px" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";

const props = withDefaults(
  defineProps<{
    modelValue: number | null;
    min?: number;
    max?: number;
    step?: number;
    precision?: number;
    unit?: string;
    placeholder?: string;
    disabled?: boolean;
  }>(),
  {
    min: 1,
    max: 99,
    step: 1,
    precision: 0,
    placeholder: "请输入",
    disabled: false,
  }
);

const emit = defineEmits<{
  (e: "update:modelValue", value: number | null): void;
}>();

const displayValue = ref<string>("");

const formatValue = (value: number | null): string => {
  if (value === null) return "";
  return String(value);
};

const parseValue = (value: string): number | null => {
  if (value.trim() === "") return null;
  const parsed = parseFloat(value);
  return isNaN(parsed) ? null : parsed;
};

watch(
  () => props.modelValue,
  (newValue) => {
    const formatted = formatValue(newValue);
    const currentNumericValue = parseValue(displayValue.value);

    if (newValue !== currentNumericValue || formatted !== displayValue.value) {
      displayValue.value = formatted;
    }
  },
  { immediate: true }
);

const isMinDisabled = computed(() => {
  if (props.modelValue === null) return false;
  return props.modelValue <= props.min;
});

const isMaxDisabled = computed(() => {
  if (props.modelValue === null) return false;
  return props.modelValue >= props.max;
});

const updateValue = (newValue: number | null) => {
  let value = newValue;
  if (value !== null) {
    if (value > props.max) value = props.max;
    if (value < props.min) value = props.min;

    const multiplier = Math.pow(10, props.precision || 0);
    value = Math.round(value * multiplier) / multiplier;
  }

  if (value !== props.modelValue) {
    emit("update:modelValue", value);
  } else {
    displayValue.value = formatValue(value);
  }
};

const decrease = () => {
  if (props.disabled || isMinDisabled.value) return;
  const currentValue = props.modelValue ?? props.min;
  updateValue(currentValue - props.step);
};

const increase = () => {
  if (props.disabled || isMaxDisabled.value) return;
  const currentValue = props.modelValue ?? props.min - props.step;
  updateValue(currentValue + props.step);
};

const handleInput = (event: Event) => {
  const input = event.target as HTMLInputElement;
  let value = input.value;

  const regex = new RegExp(`^(\\d+)?(\\.\\d{0,${props.precision}})?$`);

  if (!regex.test(value)) {
    value = displayValue.value;
  }
  input.value = value;
  displayValue.value = value;
};

const handleBlur = (event: Event) => {
  const input = event.target as HTMLInputElement;
  const value = parseValue(input.value);
  updateValue(value);
};
</script>

<style lang="scss" scoped>
.stepper-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between; /* 改为 space-between 实现分离布局 */
  width: 100%;
  gap: 16rpx; /* 添加间距 */
}

.stepper-btn {
  flex-shrink: 0;
  width: 88rpx;
  height: 88rpx; /* 确保按钮是正方形 */
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-2); /* 统一圆角 */
  color: var(--text-primary);
  transition: background-color 0.2s ease;
}

.stepper-btn:active {
  background-color: var(--bg-light-hover);
}

.stepper-btn.disabled {
  color: var(--text-disabled);
  background-color: var(--bg-input); /* 禁用时背景色不变 */
  opacity: 0.6;
  pointer-events: none;
}

.stepper-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 96rpx;
  padding: 0 24rpx; /* 调整内边距 */
  background-color: var(--bg-input); /* 使用统一的输入框背景色 */
  border-radius: var(--radius-2); /* 统一圆角 */
}

.stepper-input {
  width: 100%;
  height: 100%;
  text-align: center;
  font-size: 32rpx;
  color: var(--text-primary);
  border: none;
  background-color: var(--bg-input);
  padding: 0;
}

.stepper-unit {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-left: 12rpx; /* 调整单位间距 */
  white-space: nowrap;
}
</style>
