<template>
  <view class="stepper-wrapper" :class="[disabled && 'opacity-50']">
    <view
      class="stepper-btn minus-btn"
      :class="{ disabled: isMinDisabled }"
      @click="decrease"
    >
      <view class="i-carbon:subtract text-20px" />
    </view>
    <view class="stepper-input-container">
      <input
        v-model="displayValue"
        type="text"
        inputmode="decimal"
        class="stepper-input"
        :placeholder="placeholder"
        :disabled="disabled"
        @input="handleInput"
        @blur="handleBlur"
      />
      <text v-if="unit" class="stepper-unit">{{ unit }}</text>
    </view>
    <view
      class="stepper-btn plus-btn"
      :class="{ disabled: isMaxDisabled }"
      @click="increase"
    >
      <view class="i-carbon:add text-20px" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";

const props = withDefaults(
  defineProps<{
    modelValue: number | null;
    min?: number;
    max?: number;
    step?: number;
    precision?: number;
    unit?: string;
    placeholder?: string;
    disabled?: boolean;
    /** 业务模式：'hourly' 计时（整数）, 'piece' 计件（支持小数） */
    businessMode?: "hourly" | "piece";
  }>(),
  {
    min: 1,
    max: 99,
    step: 1,
    precision: 0,
    placeholder: "请输入",
    disabled: false,
    businessMode: undefined,
  }
);

const emit = defineEmits<{
  (e: "update:modelValue", value: number | null): void;
}>();

const displayValue = ref<string>("");

// 合并精度和步长计算，减少重复逻辑
const { computedPrecision, computedStep } = (() => {
  const precision = computed(() => {
    if (props.businessMode === "hourly") return 0;
    if (props.businessMode === "piece") return 1;
    return props.precision;
  });

  const step = computed(() => {
    if (props.businessMode === "hourly") return 1;
    if (props.businessMode === "piece") {
      return props.modelValue !== null && props.modelValue % 1 !== 0 ? 0.1 : 1;
    }
    return props.step;
  });

  return { computedPrecision: precision, computedStep: step };
})();

// 简化值处理函数
const formatValue = (value: number | null): string =>
  value === null ? "" : String(value);
const parseValue = (value: string): number | null => {
  const trimmed = value.trim();
  if (!trimmed) return null;
  const parsed = parseFloat(trimmed);
  return isNaN(parsed) ? null : parsed;
};

// 合并监听器，减少不必要的响应式更新
watch(
  () => ({ value: props.modelValue, mode: props.businessMode }),
  ({ value, mode }, old) => {
    // 处理值变化
    const formatted = formatValue(value);
    const currentNumericValue = parseValue(displayValue.value);

    if (value !== currentNumericValue || formatted !== displayValue.value) {
      displayValue.value = formatted;
    }

    // 处理模式切换
    if (old && mode !== old.mode && old.mode !== undefined) {
      emit("update:modelValue", null);
      displayValue.value = "";
    }
  },
  { immediate: true }
);

// 合并禁用状态计算
const isMinDisabled = computed(
  () => props.modelValue !== null && props.modelValue <= props.min
);
const isMaxDisabled = computed(
  () => props.modelValue !== null && props.modelValue >= props.max
);

const updateValue = (newValue: number | null) => {
  let value = newValue;
  if (value !== null) {
    // 边界值限制
    value = Math.max(props.min, Math.min(props.max, value));

    // 精度处理
    const multiplier = Math.pow(10, computedPrecision.value || 0);
    value = Math.round(value * multiplier) / multiplier;
  }

  if (value !== props.modelValue) {
    emit("update:modelValue", value);
  } else {
    displayValue.value = formatValue(value);
  }
};

const decrease = () => {
  if (props.disabled || isMinDisabled.value) return;
  const currentValue = props.modelValue ?? props.min;
  updateValue(currentValue - computedStep.value);
};

const increase = () => {
  if (props.disabled || isMaxDisabled.value) return;
  // 首次点击从1开始，而不是从0开始
  if (props.modelValue === null) {
    updateValue(props.min);
  } else {
    updateValue(props.modelValue + computedStep.value);
  }
};

const handleInput = (event: Event) => {
  const input = event.target as HTMLInputElement;
  let value = input.value;

  // 简化输入验证逻辑
  const precision = computedPrecision.value;
  const isValidInput =
    precision === 0
      ? /^\d*$/.test(value) // 整数模式
      : new RegExp(`^\\d*(\\.\\d{0,${precision}})?$`).test(value); // 小数模式

  if (!isValidInput) {
    value = displayValue.value;
  }

  input.value = value;
  displayValue.value = value;
};

const handleBlur = (event: Event) => {
  const input = event.target as HTMLInputElement;
  const value = parseValue(input.value);
  updateValue(value);
};
</script>

<style scoped>
.stepper-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between; /* 改为 space-between 实现分离布局 */
  width: 100%;
  gap: 16rpx; /* 添加间距 */
}

.stepper-btn {
  flex-shrink: 0;
  width: 88rpx;
  height: 88rpx; /* 确保按钮是正方形 */
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-2); /* 统一圆角 */
  color: var(--text-primary);
}

.stepper-btn:active {
  background-color: var(--bg-light-hover);
}

.stepper-btn.disabled {
  color: var(--text-disabled);
  background-color: var(--bg-input); /* 禁用时背景色不变 */
  opacity: 0.6;
  pointer-events: none;
}

.stepper-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 96rpx;
  padding: 0 24rpx; /* 调整内边距 */
  background-color: var(--bg-input); /* 使用统一的输入框背景色 */
  border-radius: var(--radius-2); /* 统一圆角 */
}

.stepper-input {
  width: 100%;
  height: 100%;
  text-align: center;
  font-size: 32rpx;
  color: var(--text-primary);
  border: none;
  background-color: var(--bg-input);
  padding: 0;
}

.stepper-unit {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-left: 12rpx; /* 调整单位间距 */
  white-space: nowrap;
}
</style>
