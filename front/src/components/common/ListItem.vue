<template>
  <view class="list-item" :class="{
    'list-item--clickable': clickable,
    'list-item--disabled': disabled,
    'list-item--divider': divider,
  }" :style="itemStyle" @click="handleClick">
    <!-- 左侧图标 -->
    <view v-if="icon" class="list-item__icon">
      <slot name="icon">
        <text :class="icon" :style="{
          color: iconColor,
          fontSize: iconSize,
        }" />
      </slot>
    </view>

    <!-- 主要内容区 -->
    <view class="list-item__content">
      <!-- 标题区域 -->
      <view class="list-item__header">
        <view class="list-item__title-section">
          <text v-if="title" :class="{ 'font-500': bold }" class="list-item__title">{{ title }}</text>
          <text v-if="subtitle" class="list-item__subtitle">{{
            subtitle
          }}</text>
        </view>
      </view>
    </view>

    <!-- 右侧内容区 -->
    <view class="list-item__right">
      <slot name="right">
        <view v-if="hasRightContent" class="list-item__value-section">
          <text class="list-item__value" :style="{
            color: isPlaceholder ? placeholderColor : valueColor,
          }">
            {{ displayValue }}
          </text>
          <text v-if="description" class="list-item__description">
            {{ description }}
          </text>
        </view>
      </slot>

      <!-- 右侧箭头 -->
      <view v-if="showArrow" class="list-item__arrow">
        <slot name="arrow">
          <text class="i-solar-alt-arrow-right-linear list-item__arrow-icon" :style="{ color: arrowColor }" />
        </slot>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";

/**
 * 通用列表项组件
 * 支持图标、标题、副标题、右侧内容、箭头等功能
 * 完全兼容项目的设计系统和主题
 */

interface Props {
  /** 左侧图标类名 (支持 iconify 图标) */
  icon?: string;
  /** 图标颜色 */
  iconColor?: string;
  /** 图标大小 */
  iconSize?: string;
  /** 主标题 */
  title?: string;
  /** 主标题是否加粗 */
  bold?: boolean;
  /** 副标题 */
  subtitle?: string;

  /** 右侧显示的值 */
  value?: string | number | null | undefined;
  /** 右侧描述文字 */
  description?: string;
  /** 占位符文字 (当 value 为空时显示) */
  placeholder?: string;

  /** 是否可点击 */
  clickable?: boolean;
  /** 是否显示右箭头 */
  showArrow?: boolean;
  /** 是否禁用 */
  disabled?: boolean;

  /** 右侧值文字颜色 */
  valueColor?: string;
  /** 占位符文字颜色 */
  placeholderColor?: string;
  /** 箭头颜色 */
  arrowColor?: string;

  /** 自定义内边距 */
  padding?: string;
  /** 背景颜色 */
  backgroundColor?: string;
  /** 圆角大小 */
  borderRadius?: string;
  /** 是否显示分割线 */
  divider?: boolean;

  /** 外边距 */
  margin?: string;
}

interface Emits {
  (e: "click"): void;
}

const props = withDefaults(defineProps<Props>(), {
  iconColor: "var(--text-base)",
  iconSize: "36rpx",
  clickable: true,
  showArrow: false,
  disabled: false,
  bold: true,
  valueColor: "var(--text-base)",
  placeholderColor: "var(--text-grey)",
  arrowColor: "var(--text-grey)",
  padding: "32rpx 24rpx",
  backgroundColor: "var(--bg-card)",
  borderRadius: "var(--radius-2)",
  divider: false,
  margin: "0 0 24rpx 0",
});

const emit = defineEmits<Emits>();

// 计算显示值
const displayValue = computed(() => {
  if (props.value !== undefined && props.value !== null && props.value !== "") {
    return String(props.value);
  }
  return props.placeholder || "";
});

// 是否为占位符状态
const isPlaceholder = computed(() => {
  return !props.value || props.value === "";
});

// 是否有右侧内容
const hasRightContent = computed(() => {
  return props.value !== undefined || props.placeholder || props.description;
});

// 组件样式
const itemStyle = computed(() => ({
  padding: props.padding,
  backgroundColor: props.backgroundColor,
  borderRadius: props.borderRadius,
  margin: props.margin,
}));

// 点击处理
const handleClick = () => {
  if (props.clickable && !props.disabled) {
    emit("click");
  }
};
</script>

<style lang="scss" scoped>
.list-item {
  display: flex;
  align-items: center;
  position: relative;
}

.list-item__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.list-item__content {
  flex: 1;
  min-width: 0;
}

.list-item__header {
  display: flex;
  align-items: center;
  width: 100%;
}

.list-item__title-section {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.list-item__title {
  font-size: 32rpx;
  line-height: 1.4;
}

.list-item__subtitle {
  font-size: 26rpx;
  color: var(--text-info);
  line-height: 1.3;
}

.list-item__right {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-left: auto;
  flex-shrink: 0;
}

.list-item__value-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
}

.list-item__value {
  font-size: 28rpx;
  text-align: right;
}

.list-item__description {
  font-size: 24rpx;
  color: var(--text-grey);
  text-align: right;
}

.list-item__arrow {
  display: flex;
  align-items: center;
  justify-content: center;
}

.list-item__arrow-icon {
  font-size: 36rpx;
  transition: color 0.2s ease;
}
</style>
