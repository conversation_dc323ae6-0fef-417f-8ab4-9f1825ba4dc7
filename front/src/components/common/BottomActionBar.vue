<template>
  <view class="bottom-action-bar">
    <slot></slot>
    <u-safe-bottom></u-safe-bottom>
  </view>
</template>

<script setup lang="ts">
// 目前不需要脚本逻辑，保持简洁
</script>

<style lang="scss" scoped>
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  min-height: 140rpx; /* 70px */
  padding: 32rpx; /* 12px */
  background-color: #feffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  z-index: 1000;
}
</style>
