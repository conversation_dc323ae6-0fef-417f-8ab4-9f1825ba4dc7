<template>
  <view
    class="card"
    :class="{ 'shadow-card': isShadow, 'border': showBorder }"
    :style="{
      padding: padding,
      margin: margin,
      borderRadius: borderRadius,
    }"
  >
    <!-- Header Slot -->
    <slot name="header">
      <view v-if="title" class="card-header" :style="{ marginBottom: contentPadding }">
        <view class="left">
          <view
            v-if="showTitleLine"
            class="title-line"
            :style="lineStyle"
          ></view>
          <text
            class="title-text"
            :style="{
              fontSize: titleSize,
              fontWeight: titleBold ? 500 : 'normal',
            }"
            >{{ title }}</text
          >
        </view>
        <view class="right">
          <slot name="right">
            <text v-if="rightText" class="right-text">{{ rightText }}</text>
          </slot>
        </view>
      </view>
      <view v-if="subTitle" class="sub-title" :style="{ marginBottom: contentPadding }">
        {{ subTitle }}
      </view>
    </slot>

    <!-- Content -->
    <view class="card-content">
      <slot></slot>
    </view>
  </view>
</template>

<script setup lang="ts">
import { withDefaults, defineProps, computed } from 'vue';

interface CardProps {
  /**
   * 卡片主标题
   */
  title?: string;
  /**
   * 卡片副标题，显示在主标题下方
   */
  subTitle?: string;
  /**
   * 右侧区域的简单文本内容
   */
  rightText?: string;
  /**
   * 是否显示标题左侧的竖线装饰
   * @default true
   */
  showTitleLine?: boolean;
  /**
   * 是否显示渐变色的竖线装饰
   * @default true
   */
  lineGradient?: boolean;
  /**
   * 标题竖线装饰的颜色 (当 lineGradient 为 false 时生效)
   * @default 'var(--primary)'
   */
  lineColor?: string;
  /**
   * 主标题文字大小
   * @default '32rpx'
   */
  titleSize?: string;
  /**
   * 主标题是否加粗 (500)
   * @default false
   */
  titleBold?: boolean;
  /**
   * 卡片内边距
   * @default '24rpx'
   */
  padding?: string;
  /**
   * 内容区域与头部的间距
   * @default '24rpx'
   */
  contentPadding?: string;
  /**
   * 卡片外边距
   * @default '20rpx'
   */
  margin?: string;
  /**
   * 是否显示卡片阴影
   * @default true
   */
  isShadow?: boolean;
  /**
   * 是否显示卡片边框
   * @default false
   */
  showBorder?: boolean;
  /**
   * 卡片圆角
   * @default '20rpx'
   */
  borderRadius?: string;
}

const props = withDefaults(defineProps<CardProps>(), {
  title: '',
  subTitle: '',
  rightText: '',
  showTitleLine: true,
  lineGradient: true,
  lineColor: 'var(--primary)',
  titleSize: '32rpx',
  titleBold: false,
  padding: '24rpx',
  contentPadding: '24rpx',
  margin: '20rpx',
  isShadow: true,
  showBorder: false,
  borderRadius: '20rpx',
});

const lineStyle = computed(() => {
  if (props.lineGradient) {
    return {
      background: `linear-gradient(180deg, var(--primary-400) 0%, var(--primary-600) 100%)`,
    };
  }
  return {
    backgroundColor: props.lineColor,
  };
});
</script>

<style lang="scss" scoped>
.card {
  background-color: var(--bg-card);
  overflow: hidden;

  &.border {
    border: 1rpx solid var(--border-light);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  .title-line {
    width: 8rpx;
    height: 32rpx;
    border-radius: 4rpx;
    margin-right: 12rpx;
  }

  .title-text {
    color: var(--text-base);
  }

  .right {
    flex-shrink: 0;
    margin-left: 20rpx;
  }

  .right-text {
    font-size: 28rpx;
    color: var(--text-secondary);
  }
}

.sub-title {
  font-size: 26rpx;
  color: var(--text-info);
  margin-top: -12rpx; // Adjust based on final design
}

.card-content {
  width: 100%;
  font-size: 28rpx;
  color: var(--text-secondary);
}
</style>