// 全局要用的类型放到这里
// 注意：API响应类型已统一使用 @/constants/response.ts 中的 ApiResponse<T>

declare global {

  // uni.uploadFile文件上传参数
  type IUniUploadFileOptions = {
    file?: File
    files?: UniApp.UploadFileOptionFiles[]
    filePath?: string
    name?: string
    formData?: any
  }

  enum HouseTypeEnum {
    SecondHandHouse = 1,
    NewHouse = 2,
    RentHouse = 3,
    CommercialHouse = 4,
  }

  type HouseType = {
    id: HouseTypeEnum;
    name: string;
    icon: string;
    url: string;
  }
}

export { } // 防止模块污染