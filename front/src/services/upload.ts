/**
 * Upload Service
 * 文件上传服务
 */

import { BaseService, type ServiceResponse } from './base'
import { getUploadToken } from '@/api/foo'

export interface UploadOptions {
    prefix?: string // 改为目录前缀，默认'images/'
    mimeTypes?: string[] // 允许的MIME类型
    maxSize?: number // 最大文件大小（MB）
    onProgress?: (percent: number) => void
    quality?: number // 图片质量 (0-1)
    compress?: boolean // 是否压缩
}

export interface UploadConfig {
    qiniuUploadUrl: string
    maxFileSize: number
    allowedMimeTypes: string[]
    defaultPrefix: string
}

export interface UploadResult {
    key: string
    url: string
    size: number
    mimeType: string
}

/**
 * 上传服务类
 */
export class UploadService extends BaseService {
    protected serviceName = 'UploadService'

    private config: UploadConfig = {
        qiniuUploadUrl: import.meta.env.VITE_QINIU_UPLOAD_URL || 'https://up-z1.qiniup.com',
        maxFileSize: parseInt(import.meta.env.VITE_MAX_FILE_SIZE) || 20,
        allowedMimeTypes: [
            'image/jpeg', 'image/jpg', 'image/png', 'image/webp',
            'video/mp4', 'video/quicktime'
        ],
        defaultPrefix: 'images/'
    }

    /**
     * 上传单个文件
     * @param filePath 文件路径
     * @param options 上传选项
     */
    async uploadFile(filePath: string, options: UploadOptions = {}): Promise<ServiceResponse<UploadResult>> {
        return this.handleOperation(
            async () => {
                const {
                    prefix = this.config.defaultPrefix,
                    maxSize = this.config.maxFileSize,
                    mimeTypes = this.config.allowedMimeTypes,
                    onProgress,
                    quality = 0.8,
                    compress = true
                } = options

                // 文件信息获取
                const fileInfo = await this.getFileInfo(filePath)

                // 文件验证
                this.validateFile(fileInfo, maxSize, mimeTypes)

                // 获取上传token
                const { data, code, message } = await getUploadToken()
                if (code !== 0) {
                    throw new Error(message || '获取上传凭证失败')
                }

                // 生成文件路径
                const finalKey = this.generateFilePath(prefix, fileInfo.name)

                // 执行上传
                const uploadResult = await this.performUpload(
                    filePath,
                    finalKey,
                    data.token,
                    onProgress
                )

                return {
                    key: finalKey,
                    url: `${import.meta.env.VITE_CDN_BASE_URL || ''}/${finalKey}`,
                    size: fileInfo.size,
                    mimeType: fileInfo.type
                }
            },
            {
                showLoading: true,
                loadingText: '上传中...',
                showError: true
            }
        )
    }

    /**
     * 批量上传文件
     * @param filePaths 文件路径数组
     * @param options 上传选项
     */
    async uploadMultipleFiles(
        filePaths: string[],
        options: UploadOptions = {}
    ): Promise<ServiceResponse<UploadResult[]>> {
        return this.handleBatchOperations(
            filePaths.map(filePath => () => this.uploadFile(filePath, options).then(res => res.data!)),
            {
                showLoading: true,
                loadingText: `上传中... (0/${filePaths.length})`,
                showError: true
            }
        )
    }

    /**
     * 获取文件信息
     * @param filePath 文件路径
     */
    private async getFileInfo(filePath: string): Promise<{ name: string, size: number, type: string }> {
        return new Promise((resolve, reject) => {
            uni.getFileInfo({
                filePath,
                success: (res) => {
                    const extension = filePath.split('.').pop()?.toLowerCase() || ''
                    const mimeType = this.getMimeTypeByExtension(extension)

                    resolve({
                        name: `${this.generateUniqueId()}.${extension}`,
                        size: res.size,
                        type: mimeType
                    })
                },
                fail: reject
            })
        })
    }

    /**
     * 验证文件
     * @param fileInfo 文件信息
     * @param maxSize 最大大小(MB)
     * @param allowedTypes 允许的类型
     */
    private validateFile(
        fileInfo: { size: number, type: string },
        maxSize: number,
        allowedTypes: string[]
    ): void {
        // 文件大小验证
        if (fileInfo.size > maxSize * 1024 * 1024) {
            throw new Error(`文件大小不能超过 ${maxSize}MB`)
        }

        // 文件类型验证
        if (!allowedTypes.includes(fileInfo.type)) {
            throw new Error(`不支持的文件类型: ${fileInfo.type}`)
        }
    }

    /**
     * 生成文件路径
     * @param prefix 前缀
     * @param fileName 文件名
     */
    private generateFilePath(prefix: string, fileName: string): string {
        const now = new Date()
        const datePath = [
            now.getFullYear(),
            String(now.getMonth() + 1).padStart(2, '0'),
            String(now.getDate()).padStart(2, '0'),
        ].join('-')

        return `${prefix}${datePath}/${fileName}`
    }

    /**
     * 执行上传
     * @param filePath 文件路径
     * @param key 上传key
     * @param token 上传token
     * @param onProgress 进度回调
     */
    private performUpload(
        filePath: string,
        key: string,
        token: string,
        onProgress?: (percent: number) => void
    ): Promise<any> {
        return new Promise((resolve, reject) => {
            const uploadTask = uni.uploadFile({
                url: this.config.qiniuUploadUrl,
                filePath,
                name: 'file',
                formData: {
                    token,
                    key,
                },
                success: (res) => {
                    if (res.statusCode === 200) {
                        try {
                            const result = JSON.parse(res.data as string)
                            resolve(result)
                        } catch (e) {
                            // 如果解析失败，直接返回key
                            resolve({ key })
                        }
                    } else {
                        reject(new Error(`上传失败: HTTP ${res.statusCode}`))
                    }
                },
                fail: (error) => {
                    reject(new Error(`上传失败: ${error.errMsg || '网络错误'}`))
                }
            })

            // 进度监听
            if (onProgress) {
                uploadTask.onProgressUpdate((progressEvent) => {
                    onProgress(progressEvent.progress)
                })
            }
        })
    }

    /**
     * 根据文件扩展名获取 MIME 类型
     * @param extension 文件扩展名
     */
    private getMimeTypeByExtension(extension: string): string {
        const mimeMap: Record<string, string> = {
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'gif': 'image/gif',
            'webp': 'image/webp',
            'mp4': 'video/mp4',
            'mov': 'video/quicktime',
            'avi': 'video/x-msvideo',
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        }

        return mimeMap[extension] || 'application/octet-stream'
    }

    /**
     * 生成唯一ID
     */
    private generateUniqueId(): string {
        return `${Date.now().toString(36)}-${Math.random().toString(36).substring(2)}`
    }

    /**
     * 删除上传的文件（如果支持）
     * @param key 文件key
     */
    async deleteFile(key: string): Promise<ServiceResponse<void>> {
        return this.handleOperation(
            async () => {
                // 这里可以调用删除API
                // 由于七牛云删除需要后端配合，这里只是示例
                this.log('info', `请求删除文件: ${key}`)
                // await deleteFileApi(key)
            },
            {
                showLoading: true,
                loadingText: '删除中...',
                showError: true
            }
        )
    }
}

// 创建上传服务实例
export const uploadService = new UploadService()

// 兼容性导出 - 保持向后兼容
export const putFile = (filePath: string, options: UploadOptions = {}) => {
    return uploadService.uploadFile(filePath, options).then(response => {
        if (response.success) {
            return response.data!.key
        } else {
            throw new Error(response.error || '上传失败')
        }
    })
}

// 生成唯一图片名称 - 兼容性导出
export const getUniqueImageName = () => {
    return `${Date.now().toString(36)}-${Math.random().toString(36).substring(2)}`
}

export default uploadService