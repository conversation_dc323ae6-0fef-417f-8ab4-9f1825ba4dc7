/**
 * 零工业务逻辑服务 (Composable / Hooks)
 * 职责：
 * 1. 封装与零工相关的业务逻辑和状态管理。
 * 2. 创建可复用的 Vue Composition API 函数 (Composables)。
 * 3. 调用 api/gig.ts 中定义的纯 API 请求。
 */
import { ref, computed, reactive, watch, onMounted } from 'vue'
import type { Ref } from 'vue'
import { useRequest } from 'alova/client'

import { useUserStore } from '@/stores'
import GigApi from '@/api/gig'
import { GigStatus, GigApplicationStatus } from '@/constants/gig'
import { showSuccessToast } from '@/utils'
import type {
    Gig,
    GigApplication,
    ListGigsRequest,
    CreateGigRequest,
    UpdateApplicationStatusRequest,
    ApplyGigRequest,
} from '@/types/gig'

/**
 * @description 管理零工发布/编辑表单的逻辑
 * @param gigId - (可选) 如果是编辑模式，则传入零工ID
 */
export function useGigForm(gigId?: Ref<number | null>) {
    const isEditMode = computed(() => !!gigId?.value);

    // 1. 表单数据
    const formData = reactive<CreateGigRequest>({
        title: '',
        description: '',
        salary: 0,
        salary_unit: 0,
        settlement: 0,
        people_count: 0,
        start_time: '',
        end_time: '',
        address_name: '',
        address: '',
        detail_address: '',
        latitude: 0,
        longitude: 0,
        gender: 0,
        age_min: 16,
        age_max: 65,
        experience: 0,
        education: 0,
        skills: '',
        contact_name: '',
        contact_phone: '',
        is_urgent: false,
        company_name: '',
        tags: [],
        images: [],
        approval_mode: '',
        check_in_method: '',
    });

    // 2. 在编辑模式下，获取初始数据
    const { data, loading: isLoadingInitialData, error: loadError } = useRequest(
        () => GigApi.detail(gigId?.value!),
        {
            immediate: isEditMode.value && gigId?.value && gigId.value > 0,
            watch: [gigId],
            onSuccess: (res) => {
                // 将获取到的数据填充到表单中
                if (res) {
                    Object.assign(formData, res);
                }
            }
        }
    );

    // 3. 提交表单 (创建或更新)
    const { loading: isSubmitting, error: submitError, send: submit } = useRequest(
        () => {
            const data = { ...formData };
            // 数据预处理，例如金额转换
            if (data.salary) data.salary = Math.round(data.salary * 100);

            return (isEditMode.value
                ? GigApi.update(gigId?.value!, data)
                : GigApi.create(data)) as any;
        },
        {
            immediate: false,
            onSuccess: () => {
                // 提交成功后的处理
                showSuccessToast(isEditMode.value ? '更新成功' : '发布成功');
            },
        }
    );

    return {
        formData,
        isEditMode,
        isLoadingInitialData,
        loadError,
        isSubmitting,
        submitError,
        submit,
    }
}

/**
 * @description 管理我的零工列表（manage.vue 页面）
 * @param statusFilter - 一个包含状态过滤条件的 ref 对象 (e.g., 'all', 'recruiting')
 */
export function useManageGigs(statusFilter: Ref<ListGigsRequest>) {
    // 1. 获取列表的 useRequest Hook
    const {
        data: gigs,
        loading: isLoading,
        error: fetchError,
        send: refresh
    } = useRequest(
        () => GigApi.listMine(statusFilter.value),
        {
            watch: [statusFilter], // 监听 statusFilter 的变化，自动重新请求
            immediate: true,
            initialData: { list: [] as any, total: 0 },
        }
    )

    // 2. 删除零工的 useRequest Hook
    const { loading: isDeleting, send: deleteGig } = useRequest(
        (gigId: number) => GigApi.delete(gigId),
        {
            immediate: false,
            onSuccess: () => {
                // 删除成功后自动刷新列表
                showSuccessToast('删除成功');
                refresh();
            },
        }
    );

    // 3. 返回页面所需的数据和方法
    return {
        gigs: computed(() => gigs.value?.data.list || []),
        isLoading,
        fetchError,
        isDeleting,
        deleteGig,
        refresh,
    }
}

/**
 * @description 管理特定零工的报名者列表及审核操作
 * @param gigId - 一个包含 gig ID 的 ref 对象
 */
export function useGigApplicants(gigId: Ref<number>) {
    // 1. 获取报名列表的 useRequest Hook
    const {
        data: applicants,
        loading: isLoading,
        error: fetchError,
        send: refresh
    } = useRequest(
        () => GigApi.getApplications(gigId.value, { page: 1, page_size: 100 }),
        {
            // 延迟到 gigId 有值时再发起请求
            immediate: false,
            initialData: { list: [], total: 0 },
        }
    )

    // 监听 gigId 变化，当有效时自动发起请求
    watch(
        gigId,
        (newGigId) => {
            if (newGigId > 0) {
                refresh();
            }
        },
        { immediate: true }
    );

    // 2. 执行审核操作的 useRequest Hook
    const {
        loading: isReviewing,
        error: reviewError,
        send: review
    } = useRequest(
        (payload: UpdateApplicationStatusRequest) => GigApi.reviewApplication(payload),
        {
            immediate: false, // 手动触发
            onSuccess: () => {
                // 审核成功后自动刷新列表
                showSuccessToast('操作成功');
                refresh();
            },
        }
    );

    // 3. 返回页面所需的数据和方法
    return {
        applicants,
        isLoading,
        fetchError,
        isReviewing,
        reviewError,
        review,
        refresh,
    }
}

/**
 * @description 零工详情页核心业务逻辑 - 重构版
 * 统一管理零工详情和申请状态，消除重复API调用
 * @param gigId - 零工ID的响应式引用
 */
export function useGigDetail(gigId: Ref<number>) {
    const userStore = useUserStore()

    // =================================================================
    // 1. 零工详情数据获取
    // =================================================================
    const {
        data: gigDetail,
        loading: isLoading,
        error: fetchError,
        send: refreshGigDetail
    } = useRequest(
        () => GigApi.detail(gigId.value),
        {
            immediate: false,
            initialData: null,
        }
    )

    // =================================================================
    // 2. 申请状态数据获取 - 优化调用策略
    // =================================================================
    const {
        data: applicationStatusInfo,
        loading: isLoadingApplicationStatus,
        error: applicationStatusError,
        send: refreshApplicationStatus
    } = useRequest(
        () => GigApi.checkApplicationStatus(gigId.value),
        {
            immediate: false,
            watch: [gigId.value]
        }
    )

    // =================================================================
    // 3. 智能请求控制逻辑
    // =================================================================

    // 控制零工详情请求: 当 gigId 有效时，自动发起请求
    // watch(
    //     gigId,
    //     (newGigId) => {
    //         if (newGigId > 0) {
    //             refreshGigDetail();
    //         }
    //     },
    //     { immediate: true }
    // )


    // =================================================================
    // 4. 业务状态计算
    // =================================================================

    // 用户角色判断
    const isPublisher = computed(() => {
        if (!gigDetail.value || !userStore.user) return false;
        return gigDetail.value.data.user_id === userStore.user.id
    })

    // 申请状态
    const hasApplied = computed(() => {
        return applicationStatusInfo.value?.data.has_applied || false
    })

    // 是否可以申请
    const canApply = computed(() => {
        if (!gigDetail.value || isPublisher.value || hasApplied.value) return false;
        if (gigDetail.value.data.status !== GigStatus.Recruiting) return false;
        if (gigDetail.value.data.people_count && gigDetail.value.data.current_people_count >= gigDetail.value.data.people_count) return false;
        return true;
    })

    // 操作按钮状态枚举
    const actionButtonState = computed(() => {
        if (isLoading.value) return 'loading';
        if (!gigDetail.value) return 'unavailable'; // 或者其他表示错误的state
        if (isPublisher.value) return 'publisher';
        if (hasApplied.value) return 'applied';
        if (canApply.value) return 'available';
        return 'unavailable';
    })

    // =================================================================
    // 5. 刷新方法
    // =================================================================
    const refreshAll = async () => {
        await refreshGigDetail()
        // 只有在需要时才刷新申请状态
        if (gigId.value > 0 && !isPublisher.value) {
            await refreshApplicationStatus()
        }
    }

    // 强制刷新申请状态（用于申请成功后）
    const forceRefreshApplicationStatus = async () => {
        if (gigId.value > 0) {
            await refreshApplicationStatus()
        }
    }

    // =================================================================
    // 6. 返回接口
    // =================================================================
    return {
        // 核心数据
        gigDetail,
        applicationStatusInfo,

        // 加载状态
        isLoading,
        isLoadingApplicationStatus,

        // 错误状态
        fetchError,
        applicationStatusError,

        // 业务状态
        isPublisher,
        hasApplied,
        canApply,
        actionButtonState,

        // 操作方法
        refreshGigDetail,
        refreshApplicationStatus,
        forceRefreshApplicationStatus,
        refreshAll,
    }
}

/**
 * @description 管理零工申请相关的业务逻辑
 */
export function useApplyForGig() {
    const { loading: isApplying, error: applyError, send: apply } = useRequest(
        (payload: ApplyGigRequest) => GigApi.apply(payload),
        {
            immediate: false, // 仅在手动调用 apply() 时执行
            onSuccess: () => {
                // 申请成功后的处理
                showSuccessToast('申请成功');
            },
        }
    );

    return { isApplying, applyError, apply }
}

/**
 * @description 管理零工日历页面相关的数据和操作
 * 封装月度统计和每日零工数据的获取和管理
 */
export function useGigCalendar() {
    // 月度统计数据的请求
    const {
        data: monthlyStatsData,
        loading: isLoadingMonthlyStats,
        error: monthlyStatsError,
        send: fetchMonthlyStats
    } = useRequest(
        (year: number, month: number) => GigApi.getMonthlyStats(year, month),
        { immediate: false, initialData: { total_gigs: 0, completed_gigs: 0, total_income: 0, daily_stats: [] } }
    )

    // 每日零工数据的请求
    const {
        data: dailyGigsData,
        loading: isLoadingDailyGigs,
        error: dailyGigsError,
        send: fetchDailyGigs
    } = useRequest(
        (date: string) => GigApi.getDailyGigs(date),
        { immediate: false, initialData: { gigs: [], applications: [] } }
    )

    // 解构月度统计数据
    const monthlyStats = computed(() => monthlyStatsData.value)
    const totalGigs = computed(() => monthlyStats.value?.data.total_gigs || 0)
    const completedGigs = computed(() => monthlyStats.value?.data.completed_gigs || 0)
    const pendingGigs = computed(() => totalGigs.value - completedGigs.value)

    // 解构每日零工数据
    const dailyGigs = computed(() => dailyGigsData.value?.data.gigs || [])
    const dailyApplications = computed(() => dailyGigsData.value?.data.applications || [])

    return {
        // 月度统计相关
        monthlyStats,
        totalGigs,
        completedGigs,
        pendingGigs,
        isLoadingMonthlyStats,
        monthlyStatsError,
        fetchMonthlyStats,

        // 每日零工相关
        dailyGigs,
        dailyApplications,
        isLoadingDailyGigs,
        dailyGigsError,
        fetchDailyGigs,
    }
}

/**
 * @description 管理零工列表页面（seeker）相关的数据和操作
 * 封装搜索、排序、分页等功能的状态管理
 */
export function useGigList() {
    // 当前搜索和筛选参数
    const searchParams = ref<ListGigsRequest>({
        page: 1,
        page_size: 10,
        sort_by: 'latest',
        keyword: '',
        user_lat: undefined,
        user_lon: undefined,
    })

    // 零工列表数据的请求
    const {
        data: gigsData,
        loading: isLoading,
        error: fetchError,
        send: fetchGigs
    } = useRequest(
        GigApi.list(searchParams.value),
        {
            immediate: true,
            watch: [searchParams], // 监听搜索参数变化，自动重新请求
            initialData: { list: [], total: 0 },
        },
    )

    // 解构响应数据
    const gigList = computed(() => gigsData.value?.data.list || []);
    const total = computed(() => gigsData.value?.data.total || 0);

    // 更新搜索参数的方法
    const updateSearchParams = (newParams: Partial<ListGigsRequest>) => {
        searchParams.value = { ...searchParams.value, ...newParams }
    }

    // 搜索方法
    const search = (keyword: string) => {
        updateSearchParams({ keyword, page: 1 }) // 搜索时重置页码
    }

    // 切换排序
    const changeSort = (sortBy: ListGigsRequest['sort_by']) => {
        updateSearchParams({ sort_by: sortBy, page: 1 }) // 切换排序时重置页码
    }

    // 设置用户位置
    const setUserLocation = (latitude: number, longitude: number) => {
        updateSearchParams({ user_lat: latitude, user_lon: longitude })
    }

    // 刷新列表
    const refresh = () => {
        fetchGigs()
    }

    return {
        // 数据
        gigList,
        total,
        searchParams: computed(() => searchParams.value),

        // 状态
        isLoading,
        fetchError,

        // 方法
        updateSearchParams,
        search,
        changeSort,
        setUserLocation,
        refresh,
        fetchGigs,
    }
}

/**
 * @description 管理招聘者仪表板页面（recruiter）相关的数据和操作
 * 封装我的发布列表和最新申请列表的数据管理
 */
export function useRecruiterDashboard() {
    // 获取最近发布的零工 (用于仪表板显示)
    const {
        data: recentPublishedData,
        loading: isLoadingGigs,
        error: gigsError,
        send: fetchRecentPublished
    } = useRequest(
        () => GigApi.listMine({ page: 1, page_size: 3 }),
        {
            immediate: true,
            initialData: { list: [], total: 0 }
        }
    )

    // 获取最新申请的状态
    const recentApplicationsData = ref<{ list: GigApplication[], total: number }>({ list: [], total: 0 })
    const isLoadingApps = ref(false)
    const appsError = ref<Error | null>(null)

    // 获取招聘者收到的申请列表
    const fetchRecentApplications = async () => {
        try {
            isLoadingApps.value = true
            appsError.value = null

            // 首先获取我发布的零工列表
            const myGigsResponse = await GigApi.listMine({ page: 1, page_size: 10 })
            const myGigs = myGigsResponse.data.list || []

            if (myGigs.length === 0) {
                recentApplicationsData.value = { list: [], total: 0 }
                return
            }

            // 获取每个零工的申请列表，并合并
            const allApplications: GigApplication[] = []

            for (const gig of myGigs) {
                try {
                    const applicationsResponse = await GigApi.getApplications(gig.id, { page: 1, page_size: 10 })
                    const applications = applicationsResponse.data.list || []

                    // 为每个申请添加零工信息
                    applications.forEach(app => {
                        app.gig_info = {
                            id: gig.id,
                            title: gig.title,
                            salary: gig.salary,
                            salary_unit: gig.salary_unit,
                            start_time: gig.start_time,
                            end_time: gig.end_time,
                            address_name: gig.address_name,
                            contact_name: gig.contact_name
                        }
                    })

                    allApplications.push(...applications)
                } catch (error) {
                    console.warn(`获取零工 ${gig.id} 的申请列表失败:`, error)
                }
            }

            // 按创建时间排序，最新的在前
            allApplications.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

            // 只取最新的5个申请
            const recentApps = allApplications.slice(0, 5)

            recentApplicationsData.value = {
                list: recentApps,
                total: allApplications.length
            }
        } catch (error) {
            console.error('获取最新申请失败:', error)
            appsError.value = error as Error
            recentApplicationsData.value = { list: [], total: 0 }
        } finally {
            isLoadingApps.value = false
        }
    }

    // 解构数据
    const recentPublished = computed(() => recentPublishedData.value?.data.list || []);
    const recentApplications = computed(() => recentApplicationsData.value?.list || []);

    // 刷新所有数据
    const refreshAll = async () => {
        await Promise.all([
            fetchRecentPublished(),
            fetchRecentApplications()
        ])
    }

    // 初始化时获取申请数据
    onMounted(() => {
        fetchRecentApplications()
    })

    return {
        // 我的发布数据
        recentPublished,
        isLoadingGigs,
        gigsError,
        fetchRecentPublished,

        // 最新申请数据
        recentApplications,
        isLoadingApps,
        appsError,
        fetchRecentApplications,

        // 通用方法
        refreshAll,
    }
}