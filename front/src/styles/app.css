:root,
page {
    /* ==================== 颜色系统 ==================== */
    /* 主题色 */
    --primary-50: #fff9ec;
    --primary-100: #fff1d3;
    --primary-200: #ffdfa5;
    --primary-300: #ffc66d;
    --primary-400: #ffa232;
    --primary-500: #ff860a;
    --primary-600: #ff6d00;
    --primary-700: #cc4e02;
    --primary-800: #a13d0b;
    --primary-900: #82340c;
    --primary-950: #461804;

    --primary: var(--primary-600);

    /* 文本颜色 */
    --text-red: #f52c37;
    --text-green: #22c55e;
    --text-blue: #3b82f6;
    --text-yellow: #eab308;
    --text-purple: #8b5cf6;

    --text-inverse: #feffff;
    --text-base: #212529;
    --text-secondary: #515359;
    --text-info: #8e8e93;
    --text-grey: #a1a1aa;
    --text-disable: #d4d4d8;
    --text-success: #22c55e;

    --border-color: #e5e7eb;
    --border-light: #f0f0f1;
    --border-success: #22c55e;

    /* 背景颜色 */
    --bg-page: #f4f5f9;
    --bg-tag: #f3f5f7;
    --bg-card: #ffffff;
    --bg-search: #f8f9fa;
    --bg-input: #f5f7f9;
    --bg-mask: rgba(0, 0, 0, 0.5);
    --bg-gray: #d1d5db;

    /* 功能色背景 */
    --bg-primary-light: rgba(255, 109, 0, 0.1);
    --bg-success-light: rgba(34, 197, 94, 0.1);
    --bg-warning-light: rgba(234, 179, 8, 0.1);
    --bg-danger-light: rgba(220, 38, 38, 0.1);
    --bg-info-light: rgba(59, 130, 246, 0.1);

    /* 禁用状态 */
    --bg-disabled: #f5f5f5;
    --text-disabled: var(--text-disable);

    /* 次要背景色 */
    --bg-secondary: #f8f9fa;

    /* ==================== 尺寸和间距 ==================== */
    /* 圆角尺寸 以8rpx为区间 */
    --radius: 8rpx;
    --radius-1: 8rpx;
    --radius-2: 16rpx;
    --radius-3: 24rpx;
    --radius-4: 32rpx;
    --radius-5: 40rpx;

    /* 间距尺寸 以8rpx为区间 */
    --spacing: 8rpx;
    --spacing-2: 16rpx;
    --spacing-3: 24rpx;
    --spacing-4: 32rpx;
    --spacing-5: 40rpx;
    --spacing-6: 48rpx;

    /* 行高 */
    --line-height-small: 1.25;
    --line-height-normal: 1.4;
    --line-height-large: 1.75;
}

.safe-area-inset-bottom {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
}

view {
    box-sizing: border-box;
}

image {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    /* 使用gpu加速 */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

button::after {
    border: none;
}

.page,
uni-page-wrapper,
page {
    background-color: var(--bg-page);
    font-size: 28rpx;
    color: var(--text-base);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.container {
    background: var(--bg-page);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.wrap {
    flex: 1;
    background-color: #fff;
}

.content {
    margin: 20rpx;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.bg-mask {
    background-color: var(--bg-mask);
}

.bg-primary-light {
    background-color: var(--bg-primary-light);
}

.bg-success-light {
    background-color: var(--bg-success-light);
}

.bg-warning-light {
    background-color: var(--bg-warning-light);
}

.bg-danger-light {
    background-color: var(--bg-danger-light);
}

.bg-info-light {
    background-color: var(--bg-info-light);
}

/* 文本色工具类 */
.text-primary {
    color: var(--primary);
}

.text-red {
    color: var(--text-red);
}

.text-green {
    color: var(--text-green);
}

.text-blue {
    color: var(--text-blue);
}

.text-yellow {
    color: var(--text-yellow);
}

.text-purple {
    color: var(--text-purple);
}

/* 兼容旧版类名 */
.primary {
    color: var(--primary);
}

/* 文本工具类 */
.text-line-1 {
    overflow: hidden;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-line-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.text-line-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

/* 分割线 */
.divider {
    height: 1rpx;
    background-color: var(--border-color);
    width: 100%;
    margin: var(--spacing-2) 0;
}

.divider-dashed {
    border-top: 1rpx dashed var(--border-color);
    background-color: transparent;
}

.divider-vertical {
    width: 1rpx;
    height: 100%;
    margin: 0 var(--spacing-2);
}

.tag-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    gap: 12rpx;
}

.urgent-tag {
    border: 3rpx solid var(--text-red);
    color: var(--text-red);
    font-size: 24rpx;
    font-weight: 500;
    padding: 0 6rpx;
    line-height: 1.3;
    border-radius: 8rpx;
    flex-shrink: 0;
}

/* 标签系统 */
.tag {
    display: inline-block;
    padding: 12rpx 20rpx;
    background-color: var(--bg-tag);
    color: var(--text-secondary);
    border-radius: var(--radius);
    font-size: 26rpx;
    margin-right: var(--spacing);
    margin-bottom: var(--spacing);
    line-height: 1.4;
}

.tag-sm {
    padding: 4rpx 12rpx;
    font-size: 26rpx;
}

.tag-lg {
    padding: var(--spacing-2) var(--spacing-3);
}

/* 标签类型变体 */
.tag-primary {
    background-color: var(--bg-primary-light);
    color: var(--primary);
}

.tag-success {
    background-color: var(--bg-success-light);
    color: var(--text-green);
}

.tag-warning {
    background-color: var(--bg-warning-light);
    color: var(--text-yellow);
}

.tag-danger {
    background-color: var(--bg-danger-light);
    color: var(--text-red);
}

.tag-info {
    background-color: var(--bg-info-light);
    color: var(--text-blue);
}

/* 标签边框变体 */
.tag-outlined {
    background-color: transparent;
    border: 1rpx solid var(--border-color);
}

.tag-primary-outlined {
    background-color: transparent;
    border: 1rpx solid var(--primary);
    color: var(--primary);
}

.tag-success-outlined {
    background-color: transparent;
    border: 1rpx solid var(--text-green);
    color: var(--text-green);
}

.tag-warning-outlined {
    background-color: transparent;
    border: 1rpx solid var(--text-yellow);
    color: var(--text-yellow);
}

.tag-danger-outlined {
    background-color: transparent;
    border: 1rpx solid var(--text-red);
    color: var(--text-red);
}

/* 标签容器 */
.tag-container {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: var(--spacing-2);
}

/* 带阴影的卡片 */
.box-shadow {
    box-shadow: 0 4rpx 12rpx rgba(195, 195, 210, 0.1);
}

/* 表单样式系统 */
.form-card {
    background-color: var(--bg-card);
    border-radius: var(--radius-2);
    margin-bottom: var(--spacing-3);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

.form-section {
    background-color: var(--bg-card);
    border-radius: var(--radius-2);
    padding: var(--spacing-3);
}

/* .form-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4) var(--spacing-4);
    min-height: 100rpx;
    transition: background-color 0.2s ease;
}

.form-item:last-child {
    border-bottom: none;
}

.form-item:active {
    background-color: var(--bg-search);
}

.form-item-left {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.form-item-title {
    font-size: 30rpx;
    color: var(--text-base);
    font-weight: 500;
    margin-bottom: 6rpx;
}

.form-item-desc {
    font-size: 26rpx;
    color: var(--text-info);
    line-height: 1.4;
}

.form-item-value {
    font-size: 28rpx;
    color: var(--text-secondary);
    margin-left: var(--spacing-3);
}

.form-item-empty {
    color: var(--text-info);
}

.form-item-arrow {
    font-size: 28rpx;
    color: var(--text-info);
    margin-left: var(--spacing-2);
    transform: rotate(90deg);
} */

.form-required {
    color: var(--text-red);
    margin-left: 4rpx;
}

.search-bar {
    height: 72rpx;
    padding: 0;
    background-color: var(--bg-search);
    border-radius: 36rpx;
    gap: 12rpx;
}

/* 添加缺失的CSS类 */
.bg-transparent {
    background-color: transparent;
}

.from-primary-50 {
    background: linear-gradient(to bottom, var(--primary-50), transparent);
}

/* 补充阴影类 */
.shadow-sm {
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.shadow {
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.shadow-lg {
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.action-buttons {
    display: flex;
    gap: 24rpx;
}

.prev-btn {
    flex: 1;
    height: 88rpx;
    background: var(--bg-search);
    border-radius: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2rpx solid var(--border-light);
    transition: all 0.3s ease;
}

.prev-btn:active {
    transform: scale(0.98);
    background: var(--bg-grey);
}

.prev-btn .btn-text {
    font-size: 32rpx;
    color: var(--text-base);
    font-weight: 500;
}

.publish-btn,
.next-btn {
    flex: 2;
    height: 88rpx;
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-400) 100%);
    border-radius: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 24rpx rgba(255, 109, 0, 0.4);
    transition: all 0.3s ease;
}

.publish-btn:active,
.next-btn:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 16rpx rgba(255, 109, 0, 0.6);
}

.publish-btn .btn-text,
.next-btn .btn-text {
    font-size: 32rpx;
    color: var(--text-inverse);
    font-weight: 600;
}

/* 黄色发布按钮变体 */
.publish-btn.yellow {
    background: linear-gradient(135deg, #ffd700 0%, #ffa500 100%);
    box-shadow: 0 8rpx 24rpx rgba(255, 165, 0, 0.4);
}

.publish-btn.yellow:active {
    box-shadow: 0 4rpx 16rpx rgba(255, 165, 0, 0.6);
}

.publish-btn.yellow .btn-text {
    color: var(--text-base);
}
