<template>
  <view class="container">
    <uni-nav-bar
      fixed
      :border="false"
      status-bar
      title="职位详情"
      left-icon="left"
      background-color="#ffffff"
      @clickLeft="goBack"
    />
    <scroll-view scroll-y enable-flex class="content-scroll">
      <!-- 职位基本信息卡片 -->
      <view class="card mx-20rpx my-20rpx">
        <view class="job-header flex justify-between items-start">
          <view class="job-title font-size-5 font-500 text-line-2">
            {{ jobDetail.title }}
          </view>
          <view class="job-salary text-primary text-36rpx font-500">
            {{ jobDetail.salary }}
          </view>
        </view>

        <view class="tag-box mt-28rpx">
          <view v-for="(tag, index) in jobDetail.tags" :key="index" class="tag">
            {{ tag }}
          </view>
        </view>

        <view class="flex items-center mt-28rpx text-grey">
          <text class="i-solar-map-point-linear mr-8rpx text-32rpx"></text>
          <text class="text-26rpx">{{ jobDetail.location }}</text>
        </view>
      </view>

      <!-- 招聘者信息 -->
      <view class="card mx-20rpx my-20rpx">
        <view class="recruiter-profile flex items-center justify-between">
          <view class="flex items-center flex-1">
            <image
              :src="jobDetail.recruiter.avatar"
              mode="aspectFill"
              class="recruiter-avatar"
            ></image>
            <view class="ml-20rpx">
              <view class="recruiter-name-title flex items-center">
                <text class="text-4 font-500 mr-16rpx">{{
                  jobDetail.recruiter.name
                }}</text>
                <text class="text-26rpx text-grey">{{
                  jobDetail.recruiter.title
                }}</text>
              </view>
            </view>
          </view>
          <view class="chat-btn flex-center" @tap="chatWithRecruiter">
            <text class="i-solar:chat-line-bold text-48rpx text-primary"></text>
          </view>
        </view>
      </view>

      <!-- 职位描述 -->
      <Card title="职位描述" :is-shadow="false" margin="0 20rpx" :title-bold="true">
        <view class="flex-y">
          <view class="job-duties">
            <view class="sub-title text-4 mb-20rpx">岗位职责</view>
            <view
              v-for="(duty, index) in jobDetail.duties"
              :key="index"
              class="duty-item flex mb-16rpx"
            >
              <text class="duty-dot mr-16rpx">•</text>
              <text class="duty-text">{{ duty }}</text>
            </view>
          </view>

          <view class="job-requirements mt-40rpx">
            <view class="sub-title text-4 mb-20rpx">任职要求</view>
            <view
              v-for="(req, index) in jobDetail.requirements"
              :key="index"
              class="req-item flex mb-16rpx"
            >
              <text class="req-dot mr-16rpx">•</text>
              <text class="req-text">{{ req }}</text>
            </view>
          </view>
        </view>
      </Card>

      <Card title="福利待遇" :is-shadow="false" margin="20rpx" :title-bold="true">
        <view class="flex-x gap-16rpx">
          <view
            v-for="(welfare, index) in jobDetail.welfare"
            :key="index"
            class="tag"
          >
            {{ welfare }}
          </view>
        </view>
      </Card>

      <!-- 公司信息 -->

      <Card title="公司信息" :is-shadow="false" margin="20rpx" :title-bold="true">
        <view class="flex-y gap-24rpx">
          <view class="flex items-center">
            <image
              :src="jobDetail.company.logo"
              mode="aspectFill"
              class="company-logo"
            ></image>
            <view class="ml-20rpx">
              <view class="text-4 text-base font-500">{{
                jobDetail.company.name
              }}</view>
              <view class="company-desc text-info flex items-center mt-10rpx">
                <text class="text-26rpx">{{ jobDetail.company.industry }}</text>
                <text class="mx-10rpx text-grey">|</text>
                <text class="text-26rpx">{{ jobDetail.company.size }}</text>
              </view>
            </view>
          </view>

          <view class="flex-x gap-16rpx">
            <view
              v-for="(tag, index) in jobDetail.company.tags"
              :key="index"
              class="tag"
            >
              {{ tag }}
            </view>
          </view>
        </view>
      </Card>

      <!-- 安全提示 -->
      <view class="safety-tips flex-col card mx-20rpx my-20rpx">
        <view class="flex items-baseline">
          <view class="safety-icon mr-16rpx">
            <text class="i-carbon-security text-36rpx text-blue"></text>
          </view>
          <view class="text-4 font-500 mb-16rpx text-left">求职安全提示</view>
        </view>
        <view class="flex-col">
          <view class="text-26rpx text-info leading-loose text-left">
            平台严禁用人单位和招聘者用户做出任何损害求职者合法权益的违法违规行为，包括但不限于扣押求职者证件、收取求职者财物、向求职者集资、让求职者入股、诱导求职者异地入职、违法违规使用求职者简历等。您一旦发现此类行为，请立即举报。
          </view>
          <view class="safety-link text-26rpx text-blue mt-16rpx">
            了解更多职场安全防范知识
            <text class="i-carbon-chevron-right text-20rpx"></text>
          </view>
        </view>
      </view>

      <!-- 底部间距，确保内容不被底部操作栏遮挡 -->
      <view class="bottom-padding"></view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-action-bar flex items-center">
      <view class="secondary-btns flex">
        <view class="secondary-btn flex-col items-center" @tap="toggleCollect">
          <text
            class="i-carbon-star text-40rpx"
            :class="isCollected ? 'text-red' : 'text-grey'"
          ></text>
          <text class="text-24rpx mt-4rpx text-info">收藏</text>
        </view>
        <view class="secondary-btn flex-col items-center" @tap="shareJob">
          <text class="i-carbon-share text-40rpx text-grey"></text>
          <text class="text-24rpx mt-4rpx text-info">分享</text>
        </view>
      </view>

      <view class="primary-btns flex-1">
        <view class="contact-btn flex-1" @tap="callPhone">联系招聘者</view>
        <view class="apply-btn flex-1 interactive-scale" @tap="applyJob">立即应聘</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from "vue";
import Card from "@/components/common/Card.vue";

// 收藏状态
const isCollected = ref(false);

// 获取URL参数
const jobId = ref("");
onMounted(() => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options || currentPage.$page.options;
  if (options && options.id) {
    jobId.value = options.id;
    console.log("获取到职位ID:", jobId.value);
    // 实际开发中，应该根据ID从服务器获取职位详情
  }
});

// 模拟职位详情数据
const jobDetail = ref({
  title: "高级前端开发工程师",
  salary: "25-35K·14薪",
  tags: ["5-10年", "本科", "全职", "前端开发", "Vue", "React"],
  location: "北京市朝阳区望京SOHO",
  company: {
    logo: "https://picsum.photos/seed/company6/100/100",
    name: "字节跳动",
    industry: "互联网",
    size: "10000人以上",
    tags: ["上市公司", "五险一金", "年终奖", "定期体检", "餐补"],
  },
  duties: [
    "参与公司核心产品的前端开发，负责web端的页面开发和优化",
    "根据产品需求，配合产品经理和UI设计师，实现产品的前端界面和交互效果",
    "参与前端架构设计和技术选型，提升开发效率和代码质量",
    "与后端开发工程师协作，完成数据对接和接口联调",
    "持续优化前端性能，提升用户体验",
  ],
  requirements: [
    "本科及以上学历，计算机相关专业，5年以上前端开发经验",
    "熟练掌握HTML5/CSS3/JavaScript，熟悉ES6+语法和特性",
    "精通Vue/React等主流前端框架，有大型项目开发经验",
    "熟悉前端工程化和模块化开发，熟练使用Webpack等打包工具",
    "有良好的代码风格，注重代码质量，有性能优化经验",
    "具备良好的团队协作精神和沟通能力，能够独立解决问题",
  ],
  welfare: [
    "五险一金",
    "带薪年假",
    "年终奖",
    "定期体检",
    "节日福利",
    "团队建设",
    "技术培训",
    "晋升机会",
    "免费班车",
    "加班补助",
  ],
  address: "北京市朝阳区望京SOHO T1座25层",
  recruiter: {
    avatar: "https://picsum.photos/seed/recruiter1/100/100",
    name: "张经理",
    title: "资深HR",
    responseTime: "3小时",
    responseRate: "94%",
  },
});

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 切换收藏状态
const toggleCollect = () => {
  isCollected.value = !isCollected.value;
  uni.showToast({
    title: isCollected.value ? "已收藏" : "已取消收藏",
    icon: "none",
  });
};

// 分享职位
const shareJob = () => {
  uni.showShareMenu({
    withShareTicket: true,
    menus: ["shareAppMessage", "shareTimeline"],
  });
};

// 联系招聘者
const callPhone = () => {
  uni.showModal({
    title: "提示",
    content: "是否拨打招聘者电话？",
    success: function (res) {
      if (res.confirm) {
        uni.makePhoneCall({
          phoneNumber: "10086",
          fail: () => {
            uni.showToast({
              title: "拨打电话失败",
              icon: "none",
            });
          },
        });
      }
    },
  });
};

// 立即应聘
const applyJob = () => {
  uni.showToast({
    title: "已投递简历",
    icon: "success",
  });
};

// 与招聘者聊天
const chatWithRecruiter = () => {
  uni.showToast({
    title: "正在连接聊天...",
    icon: "none",
  });
  // 实际开发中，这里应该跳转到聊天页面
  // uni.navigateTo({
  //   url: `/pages/message/chat?userId=${jobDetail.value.recruiter.id}&type=recruiter`
  // });
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  padding-bottom: env(safe-area-inset-bottom);
}

.content-scroll {
  flex: 1;
}

.job-title {
  max-width: 60%;
  line-height: 1.4;
}

.job-salary {
  line-height: 1.8;
}

.max-w-75 {
  max-width: 75%;
}

.company-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: var(--radius-2);
}

.section-title-line {
  width: 8rpx;
  height: 32rpx;
  border-radius: 4rpx;
}

.recruiter-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}

.chat-btn {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background-color: var(--bg-primary-light);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s;


}

.bottom-action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 160rpx;
  background-color: var(--bg-card);
  padding: var(--spacing);
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 99;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.secondary-btns {
  height: 100%;
  padding-right: var(--spacing);
  display: flex;
  align-items: center;
}

.secondary-btn {
  position: relative;
  padding: 0 20rpx;
  justify-content: center;
}

.primary-btns {
  height: 100%;
  display: flex;
  gap: var(--spacing-2);
  padding-left: var(--spacing-2);
}

.contact-btn {
  height: 80rpx;
  line-height: 80rpx;
  background-color: var(--bg-tag);
  text-align: center;
  border-radius: 40rpx;
  font-weight: bold;
  color: var(--text-secondary);
}

.apply-btn {
  height: 80rpx;
  line-height: 80rpx;
  background-color: var(--primary);
  color: var(--text-inverse);
  text-align: center;
  border-radius: 40rpx;
  font-weight: bold;
}

.bottom-padding {
  height: 220rpx; /* 增加底部内容的间距，避免被底部栏遮挡 */
}

.safety-link {
  display: flex;
  align-items: center;
}
</style>
