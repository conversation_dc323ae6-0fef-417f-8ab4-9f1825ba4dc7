<template>
  <PageLayout>
    <view class="content">
      <!-- 头像区域 -->
      <view class="avatar-section">
        <button class="avatar-button" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
          <NetworkImage :src="userInfo.avatar" placeholder="/static/images/default-avatar.png" mode="aspectFill"
            width="200rpx" height="200rpx" radius="50%" />
        </button>
      </view>

      <!-- 基本信息 -->
      <view class="card p-0 mb-3">
        <ListItem title="昵称" :value="userInfo?.nickname" placeholder="未设置" show-arrow @click="editNickname" />
        <ListItem title="真实姓名" :value="userInfo?.real_name" placeholder="未设置" show-arrow @click="editRealName" />
        <ListItem title="性别" :value="formatGender(userInfo?.gender)" placeholder="未设置" show-arrow
          @click="showGenderPicker" />
        <ListItem title="生日" :value="formatBirthday(userInfo?.birthday)" placeholder="未设置" show-arrow
          @click="showBirthdayPicker" />
      </view>

      <!-- 联系方式 -->
      <ListItem title="手机号" :value="formatPhoneNumber(userInfo.phone)" show-arrow @click="editPhone" />
    </view>

    <!-- 性别选择器弹窗 -->
    <uni-popup ref="genderPopup" type="bottom" :safe-area="false">
      <view class="picker-popup">
        <view class="picker-header">
          <text class="picker-cancel" @click="closeGenderPicker">取消</text>
          <text class="picker-title">选择性别</text>
          <text class="picker-confirm" @click="confirmGender">确定</text>
        </view>
        <picker-view :value="genderPickerValue" @change="onGenderChange" class="picker-view">
          <picker-view-column>
            <view v-for="(item, index) in genderOptions" :key="index" class="picker-item">
              {{ item }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>

    <!-- 生日选择器弹窗 -->
    <uni-popup ref="birthdayPopup" type="bottom" :safe-area="false">
      <view class="picker-popup">
        <view class="picker-header">
          <text class="picker-cancel" @click="closeBirthdayPicker">取消</text>
          <text class="picker-title">选择生日</text>
          <text class="picker-confirm interactive-scale" @click="confirmBirthday">确定</text>
        </view>
        <picker-view :value="birthdayPickerValue" @change="onBirthdayChange" class="picker-view">
          <picker-view-column>
            <view v-for="year in years" :key="year" class="picker-item">
              {{ year }}年
            </view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="month in months" :key="month" class="picker-item">
              {{ month }}月
            </view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="day in days" :key="day" class="picker-item">
              {{ day }}日
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>
  </PageLayout>
</template>

<script setup lang="ts">
import { useUserStore } from "@/stores/user";
import { putFile } from "@/services/upload";
import { updateUserProfile } from "@/api/user";
import { showToast, formatPhoneNumber } from "@/utils";
import NetworkImage from "@/components/NetworkImage.vue";
import ListItem from "@/components/common/ListItem.vue";
import Card from "@/components/common/Card.vue";
import PageLayout from "@/components/PageLayout.vue";

const userStore = useUserStore();
const userInfo = computed(() => userStore.getUser);

// 弹窗引用
const genderPopup = ref();
const birthdayPopup = ref();

// 性别选择相关
const genderOptions = ref(["男", "女"]);
const genderPickerValue = ref([0]);
const selectedGender = ref(0); // 0: 男, 1: 女

// 生日选择相关
const currentYear = new Date().getFullYear();
const years = ref(Array.from({ length: 80 }, (_, i) => currentYear - i));
const months = ref(Array.from({ length: 12 }, (_, i) => i + 1));
const days = ref(Array.from({ length: 31 }, (_, i) => i + 1));
const birthdayPickerValue = ref([30, 0, 0]); // 默认1994年1月1日
const selectedBirthday = ref({ year: 1994, month: 1, day: 1 });

// 头像更换相关方法
const onChooseAvatar = async (e: any) => {
  try {
    const avatarUrl = e.detail.avatarUrl;
    if (avatarUrl) {
      await uploadAvatarFromPath(avatarUrl);
    }
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "获取微信头像失败";
    uni.showToast({ title: errorMessage, icon: "none" });
  }
};

const uploadAvatarFromPath = async (filePath: string) => {
  uni.showLoading({ title: "上传中..." });

  // 使用putFile上传图片
  const uploadKey = await putFile(filePath);

  if (uploadKey) {
    await updateUserProfile({ avatar: uploadKey })
      .then(() => {
        userStore.fetchUserProfile();
      })
      .finally(() => {
        uni.hideLoading();
        genderPopup.value?.close();
      });
  } else {
    showToast("上传失败，请重试");
  }
};

const editNickname = () => {
  uni.navigateTo({
    url: "./edit-field?field=nickname&title=昵称",
  });
};

const editRealName = () => {
  uni.navigateTo({
    url: "./edit-field?field=real_name&title=真实姓名",
  });
};

const editPhone = () => {
  uni.navigateTo({
    url: "/pages/mine/account/profile/change-phone",
  });
};

// 格式化生日显示
const formatBirthday = (birthday?: string) => {
  if (!birthday) return "";
  const date = new Date(birthday);
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
};

// 格式化性别显示
const formatGender = (gender?: number) => {
  if (gender === undefined || gender === null) return "";
  return genderOptions.value[gender] || "";
};

// 性别选择相关方法
const showGenderPicker = () => {
  // 设置当前性别对应的索引
  const currentGender = userInfo.value?.gender;
  if (currentGender !== undefined) {
    genderPickerValue.value = [currentGender];
    selectedGender.value = currentGender;
  }
  genderPopup.value?.open();
};

const onGenderChange = (e: any) => {
  const index = e.detail.value[0];
  selectedGender.value = index;
};

const confirmGender = async () => {
  uni.showLoading({ title: "更新中..." });
  await updateUserProfile({ gender: selectedGender.value })
    .then(() => {
      userStore.fetchUserProfile();
    })
    .finally(() => {
      uni.hideLoading();
      genderPopup.value?.close();
    });
};

const closeGenderPicker = () => {
  genderPopup.value?.close();
};

// 生日选择相关方法
const showBirthdayPicker = () => {
  // 设置当前生日对应的索引
  const currentBirthday = userInfo.value?.birthday;
  if (currentBirthday) {
    const date = new Date(currentBirthday);
    const yearIndex = years.value.indexOf(date.getFullYear());
    const monthIndex = date.getMonth();
    const dayIndex = date.getDate() - 1;
    birthdayPickerValue.value = [
      yearIndex >= 0 ? yearIndex : 30,
      monthIndex,
      dayIndex,
    ];
    selectedBirthday.value = {
      year: date.getFullYear(),
      month: date.getMonth() + 1,
      day: date.getDate(),
    };
  }
  birthdayPopup.value?.open();
};

const onBirthdayChange = (e: any) => {
  const [yearIndex, monthIndex, dayIndex] = e.detail.value;
  selectedBirthday.value = {
    year: years.value[yearIndex],
    month: months.value[monthIndex],
    day: days.value[dayIndex],
  };
};

const confirmBirthday = async () => {
  const { year, month, day } = selectedBirthday.value;
  const birthdayString = `${year}-${String(month).padStart(2, "0")}-${String(
    day
  ).padStart(2, "0")}`;

  try {
    uni.showLoading({ title: "更新中..." });
    await updateUserProfile({ birthday: birthdayString })
      .then(() => {
        userStore.fetchUserProfile();
        uni.showToast({ title: "更新成功", icon: "success" });
      })
      .finally(() => {
        uni.hideLoading();
      });
  } catch (error) {
    uni.hideLoading();
  } finally {
    birthdayPopup.value?.close();
  }
};

const closeBirthdayPicker = () => {
  birthdayPopup.value?.close();
};

// 工具方法
const getGenderText = (gender?: number) => {
  switch (gender) {
    case 1:
      return "男";
    case 2:
      return "女";
    default:
      return "未设置";
  }
};
</script>

<style lang="scss" scoped>
.content {
  padding: 0;
}

// 头像区域
.avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: 48rpx;
}

.avatar-button {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  padding: 0;
  border: 6rpx solid #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  background-color: transparent;

  &::after {
    border: none;
  }
}

// Picker弹窗样式
.picker-popup {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.picker-cancel,
.picker-confirm {
  font-size: 28rpx;
  color: var(--primary);
  min-width: 64rpx;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.picker-view {
  height: 400rpx;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 30rpx;
  color: var(--text-primary);
}
</style>
