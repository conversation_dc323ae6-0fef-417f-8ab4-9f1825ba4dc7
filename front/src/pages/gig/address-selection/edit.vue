<template>
  <view class="container">

    <!-- 表单内容 -->
    <view class="form-content">
      <!-- 地址标题 -->
      <view class="form-section">
        <view class="form-header">
          <text class="form-label">地址标题</text>
        </view>
        <view class="form-input-wrapper">
          <input 
            class="form-input"
            v-model="formData.title"
            placeholder="请输入地址标题，如：公司、家等"
            maxlength="20"
          />
        </view>
      </view>

      <!-- 详细地址 -->
      <view class="form-section">
        <view class="form-header">
          <text class="form-label">详细地址</text>
        </view>
        <view class="form-input-wrapper">
          <textarea 
            class="form-textarea"
            v-model="formData.detail"
            placeholder="请输入详细地址信息"
            maxlength="100"
            :show-count="true"
          />
        </view>
      </view>

      <!-- 设为默认 -->
      <view class="form-section">
        <view class="form-item-row" @click="toggleDefault">
          <view class="form-item-left">
            <text class="form-label">设为默认地址</text>
            <text class="form-desc">默认地址会优先显示在列表顶部</text>
          </view>
          <view class="form-item-right">
            <view class="switch-wrapper">
              <view class="switch" :class="{ 'active': formData.isDefault }">
                <view class="switch-thumb"></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <BottomActionBar>
      <view class="bottom-actions">
        <view class="action-buttons">
          <view class="cancel-btn" @click="goBack">
            <text class="cancel-text">取消</text>
          </view>
          <view class="save-btn" @click="saveAddress" :class="{ 'disabled': !canSave }">
            <text class="save-text">{{ isEdit ? '保存' : '添加' }}</text>
          </view>
        </view>
      </view>
    </BottomActionBar>
  </view>
</template>

<script setup lang="ts">
import BottomActionBar from "@/components/common/BottomActionBar.vue";

interface AddressForm {
  id?: string;
  title: string;
  detail: string;
  isDefault: boolean;
}

// 页面参数
const mode = ref('add'); // 'add' 或 'edit'
const addressId = ref('');
const isEdit = computed(() => mode.value === 'edit');

// 表单数据
const formData = ref<AddressForm>({
  title: "",
  detail: "",
  isDefault: false,
});

// 计算是否可以保存
const canSave = computed(() => {
  return formData.value.title.trim() && formData.value.detail.trim();
});

// 页面加载时处理参数
onLoad((options: any) => {
  mode.value = options.mode || 'add';
  
  if (mode.value === 'edit' && options.id) {
    addressId.value = options.id;
    formData.value.title = decodeURIComponent(options.title || '');
    formData.value.detail = decodeURIComponent(options.detail || '');
    formData.value.isDefault = options.isDefault === 'true';
    formData.value.id = options.id;
  }
});

// 页面加载
onMounted(() => {
  if (isEdit.value && addressId.value) {
    loadAddressDetail();
  }
});

// 加载地址详情
const loadAddressDetail = async () => {
  try {
    // TODO: 调用API获取地址详情
    // 模拟数据
    if (addressId.value === "1") {
      formData.value = {
        id: "1",
        title: "金田影视传媒产业园-12号楼",
        detail: "北京市朝阳区金田影视传媒产业园1单元门口",
        isDefault: true,
      };
    }
  } catch (error) {
    console.error("加载地址详情失败:", error);
    uni.showToast({
      title: "加载失败",
      icon: "error",
    });
  }
};

// 切换默认状态
const toggleDefault = () => {
  formData.value.isDefault = !formData.value.isDefault;
};

// 保存地址
const saveAddress = async () => {
  if (!canSave.value) {
    return;
  }

  try {
    const addressData = {
      ...formData.value,
      id: mode.value === 'edit' ? addressId.value : Date.now().toString()
    };
    
    console.log(`${mode.value === 'add' ? '新增' : '编辑'}地址:`, addressData);
    
    uni.showToast({
      title: mode.value === 'add' ? "新增成功" : "保存成功",
      icon: "success",
    });
    
    setTimeout(() => {
      goBack();
    }, 1500);
  } catch (error) {
    console.error("保存地址失败:", error);
    uni.showToast({
      title: "保存失败",
      icon: "error",
    });
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: var(--bg-page);
  padding-bottom: 140rpx;
}

/* 表单内容 */
.form-content {
  padding: 24rpx;
  margin-top: calc(88rpx + var(--status-bar-height, 44rpx));
}

.form-section {
  background: var(--bg-card);
  border-radius: var(--radius-3);
  padding: 32rpx 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 8rpx rgba(195, 195, 210, 0.1);
}

.form-header {
  margin-bottom: 24rpx;
}

.form-label {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-base);
}

.form-desc {
  font-size: 24rpx;
  color: var(--text-info);
  margin-top: 8rpx;
  display: block;
}

/* 输入框样式 */
.form-input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background: var(--bg-input);
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-2);
  padding: 0 24rpx;
  font-size: 28rpx;
  color: var(--text-base);
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: var(--primary);
  background: var(--bg-card);
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  background: var(--bg-input);
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-2);
  padding: 24rpx;
  font-size: 28rpx;
  color: var(--text-base);
  line-height: 1.4;
  resize: none;
  transition: all 0.3s ease;
}

.form-textarea:focus {
  border-color: var(--primary);
  background: var(--bg-card);
}

/* 表单行样式 */
.form-item-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80rpx;
}

.form-item-left {
  flex: 1;
}

.form-item-right {
  margin-left: 24rpx;
}

/* 开关样式 */
.switch-wrapper {
  padding: 8rpx;
}

.switch {
  width: 96rpx;
  height: 56rpx;
  background: var(--bg-gray);
  border-radius: 28rpx;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
}

.switch.active {
  background: var(--primary);
}

.switch-thumb {
  width: 48rpx;
  height: 48rpx;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.switch.active .switch-thumb {
  transform: translateX(40rpx);
}

/* 底部按钮 */
.bottom-actions {
  width: 100%;
}

.action-buttons {
  display: flex;
  gap: 24rpx;
}

.cancel-btn {
  flex: 1;
  height: 88rpx;
  background: var(--bg-search);
  border-radius: var(--radius-2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.cancel-btn:active {
  transform: scale(0.98);
  background: var(--bg-tag);
}

.cancel-text {
  font-size: 32rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

.save-btn {
  flex: 2;
  height: 88rpx;
  background: var(--primary);
  border-radius: var(--radius-2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.save-btn:active:not(.disabled) {
  transform: scale(0.98);
  background: var(--primary-700);
}

.save-btn.disabled {
  background: var(--bg-disable);
  cursor: not-allowed;
}

.save-text {
  font-size: 32rpx;
  color: white;
  font-weight: 500;
}

.save-btn.disabled .save-text {
  color: var(--text-disable);
}
</style>