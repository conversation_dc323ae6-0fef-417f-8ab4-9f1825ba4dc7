<template>
  <view class="container pb-180rpx">
    <!-- 表单内容 -->
    <!-- 地址标题 -->
    <Card title="工作地点">
      <view class="form-input-wrapper content-space">
        <input
          class="form-input"
          v-model="formData.name"
          placeholder="请输入地址标题，如：公司、家等"
          :disabled="true"
          @click="showAddressSelector"
        />
      </view>
    </Card>

    <!-- 详细地址 -->
    <Card title="详细地址">
      <view class="content-space">
        <tui-textarea
          v-model="formData.fullAddress"
          placeholder="请补充路号+详细门牌号，例如南京东路888号1层101室右拐"
          :maxlength="100"
          :isCounter="true"
          padding="24rpx"
          minHeight="80rpx"
          height="110rpx"
          backgroundColor="var(--bg-input)"
          :borderTop="false"
          :borderBottom="false"
          radius="16rpx"
          :size="30"
          borderColor="transparent"
        ></tui-textarea>
      </view>
    </Card>

    <!-- 找路指引 -->
    <Card
      title="找路指引（上传门头照，非必填）"
      subTitle="例如进大门右拐上楼，请勿填写手机或微信号"
    >
      <view class="guide-section">
        <!-- 上传区域 -->
        <view class="upload-section">
          <view class="upload-grid">
            <!-- 已上传的图片 -->
            <view
              v-for="(image, index) in formData.guideImages"
              :key="index"
              class="upload-item"
            >
              <image :src="image" class="upload-image" mode="aspectFill" />
              <view class="delete-btn" @click="removeImage(index)">
                <view
                  class="i-solar:close-circle-bold text-32rpx text-white"
                ></view>
              </view>
            </view>

            <!-- 上传按钮 -->
            <view
              v-if="formData.guideImages.length < 6"
              class="upload-btn"
              @click="showUploadOptions"
            >
              <view
                class="i-solar:camera-bold text-64rpx text-secondary mb-12rpx"
              ></view>
              <text class="text-info text-24rpx">上传照片</text>
            </view>
          </view>

          <view class="upload-hint">
            <text class="text-info text-24rpx"
              >地点不好找，建议上传照片或视频</text
            >
            <text class="text-primary text-24rpx ml-16rpx" @click="showExample"
              >查看示例</text
            >
          </view>
        </view>
      </view>
    </Card>

    <!-- 设为默认 -->
    <Card margin="24rpx 0">
      <view class="flex-x-between">
        <view class="flex-1">
          <view class="text-base text-32rpx font-weight-500 mb-8rpx"
            >设为默认地址</view
          >
          <view class="text-info text-26rpx">默认地址会优先显示在列表顶部</view>
        </view>
        <view class="switch-wrapper ml-24rpx">
          <tui-switch
            :checked="formData.isDefault"
            :color="'var(--primary)'"
            @change="handleSwitchChange"
          />
        </view>
      </view>
    </Card>

    <!-- 底部按钮 -->
    <BottomActionBar>
      <view class="action-buttons" :class="{ 'single-button': !isEdit }">
        <!-- 删除按钮 - 仅编辑模式显示 -->
        <view v-if="isEdit" class="delete-btn" @click="deleteAddress">
          <text class="delete-text text-red">删除</text>
        </view>
        <!-- 保存按钮 -->
        <view
          class="save-btn"
          @click="saveAddress"
          :class="{ disabled: !canSave }"
        >
          <text class="save-text">保存</text>
        </view>
      </view>
    </BottomActionBar>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import BottomActionBar from "@/components/common/BottomActionBar.vue";
import Card from "@/components/common/Card.vue";
import tuiTextarea from "@/components/thorui/tui-textarea/tui-textarea.vue";
import tuiSwitch from "@/components/thorui/tui-switch/tui-switch.vue";
import type { CreateAddressRequest, UpdateAddressRequest } from "@/types/user";
import UserApi from "@/api/user";
import { callApi } from "@/utils/network/helpers";

interface AddressForm {
  id?: number;
  name: string;
  address: string;
  detailAddress: string;
  fullAddress: string;
  latitude: number;
  longitude: number;
  navigationGuide: string;
  guideImages: string[];
  isDefault: boolean;
}

interface PageParams {
  mode?: "add" | "edit";
  id?: string;
  name?: string;
  fullAddress?: string;
  navigationGuide?: string;
  isDefault?: string;
}

// 页面参数
const mode = ref<"add" | "edit">("add");
const addressId = ref(0);
const isEdit = computed(() => mode.value === "edit");

// 表单数据 - 使用reactive更适合复杂对象
const formData = reactive<AddressForm>({
  name: "",
  address: "",
  detailAddress: "",
  fullAddress: "",
  latitude: 0,
  longitude: 0,
  navigationGuide: "",
  guideImages: [],
  isDefault: false,
});

// 计算是否可以保存
const canSave = computed(() => {
  return formData.name.trim() && formData.fullAddress.trim();
});

// 解析页面参数的工具函数
const parsePageParams = (options: Record<string, any>): PageParams => {
  return {
    mode: (options.mode as "add" | "edit") || "add",
    id: options.id,
    name: options.name ? decodeURIComponent(options.name) : undefined,
    fullAddress: options.fullAddress
      ? decodeURIComponent(options.fullAddress)
      : undefined,
    navigationGuide: options.navigationGuide
      ? decodeURIComponent(options.navigationGuide)
      : undefined,
    isDefault: options.isDefault,
  };
};

// 初始化表单数据
const initFormData = (params: PageParams) => {
  if (params.mode === "edit" && params.id) {
    addressId.value = parseInt(params.id);
    // 先用URL参数填充基本信息，然后加载详细信息
    Object.assign(formData, {
      id: parseInt(params.id),
      name: params.name || "",
      fullAddress: params.fullAddress || "",
      navigationGuide: params.navigationGuide || "",
      isDefault: params.isDefault === "true",
    });
    // 加载完整的地址详情
    loadAddressDetail();
  }
};

// 页面加载时处理参数
onLoad((options: Record<string, any>) => {
  const params = parsePageParams(options);
  mode.value = params.mode || "add";
  initFormData(params);
});

// 加载地址详情
const loadAddressDetail = async () => {
  try {
    const result = await callApi(
      () => UserApi.getAddressDetail(addressId.value),
      { showLoading: true, loadingText: "加载中..." }
    );
    if (result.success && result.data) {
      Object.assign(formData, result.data);
    }
  } catch (error) {
    console.error("加载地址详情失败:", error);
    uni.showToast({
      title: "加载地址信息失败",
      icon: "none",
    });
  }
};

// 显示地址选择器
const showAddressSelector = () => {
  uni.chooseLocation({
    success: (res) => {
      formData.name = res.name || "";
      formData.address = res.address || "";
      formData.latitude = res.latitude;
      formData.longitude = res.longitude;
      // 自动生成完整地址
      formData.fullAddress = `${res.address || ""}${formData.detailAddress}`;
    },
    fail: (error) => {
      console.error("选择位置失败:", error);
      uni.showToast({
        title: "选择位置失败",
        icon: "none",
      });
    },
  });
};

// 处理switch变化
const handleSwitchChange = (e: any) => {
  formData.isDefault = e.detail.value;
};

// 显示上传选项
const showUploadOptions = () => {
  uni.showActionSheet({
    itemList: ["拍照", "从相册选择"],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          chooseImage("camera");
          break;
        case 1:
          chooseImage("album");
          break;
      }
    },
  });
};

// 选择图片
const chooseImage = (sourceType: "camera" | "album") => {
  const maxCount = 6 - formData.guideImages.length;
  if (maxCount <= 0) {
    uni.showToast({
      title: "最多只能上传6张图片",
      icon: "none",
    });
    return;
  }

  uni.chooseImage({
    count: maxCount,
    sizeType: ["compressed"],
    sourceType: [sourceType],
    success: (res) => {
      // TODO: 上传到服务器
      formData.guideImages.push(...res.tempFilePaths);
    },
    fail: (error) => {
      console.error("选择图片失败:", error);
    },
  });
};

// 删除图片
const removeImage = (index: number) => {
  formData.guideImages.splice(index, 1);
};

// 显示示例
const showExample = () => {
  uni.showModal({
    title: "找路指引示例",
    content: "例如：进大门后右转，上楼梯到二楼，找到201房间门口等候",
    showCancel: false,
  });
};

// 保存地址
const saveAddress = async () => {
  if (!canSave.value) {
    return;
  }

  try {
    if (mode.value === "add") {
      const createData: CreateAddressRequest = {
        name: formData.name,
        address: formData.address,
        detail_address: formData.detailAddress,
        full_address: formData.fullAddress,
        latitude: formData.latitude,
        longitude: formData.longitude,
        navigation_guide: formData.navigationGuide,
        guide_images: formData.guideImages,
      };

      await callApi(() => UserApi.createAddress(createData), {
        loadingText: "创建中...",
        successText: "新增成功",
        onSuccess: () => {
          setTimeout(() => goBack(), 1500);
        },
      });
    } else {
      const updateData: UpdateAddressRequest = {
        name: formData.name,
        address: formData.address,
        detail_address: formData.detailAddress,
        full_address: formData.fullAddress,
        latitude: formData.latitude,
        longitude: formData.longitude,
        navigation_guide: formData.navigationGuide,
        guide_images: formData.guideImages,
      };

      const result = await callApi(
        () => UserApi.updateAddress(addressId.value, updateData),
        {
          loadingText: "保存中...",
          successText: "保存成功",
        }
      );

      if (result.success) {
        // 如果设置为默认地址，需要额外调用设置默认的API
        if (formData.isDefault) {
          await callApi(() => UserApi.setDefaultAddress(addressId.value));
        }

        setTimeout(() => goBack(), 1500);
      }
    }
  } catch (error) {
    console.error("保存地址失败:", error);
    uni.showToast({
      title: "保存失败，请重试",
      icon: "none",
    });
  }
};

// 删除地址
const deleteAddress = async () => {
  if (!isEdit.value) {
    return;
  }

  uni.showModal({
    title: "确认删除",
    content: "删除后无法恢复，确定要删除这个地址吗？",
    confirmText: "删除",
    confirmColor: "#ff4757",
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await callApi(
            () => UserApi.deleteAddress(addressId.value),
            {
              loadingText: "删除中...",
              successText: "删除成功",
            }
          );

          if (result.success) {
            setTimeout(() => goBack(), 1500);
          }
        } catch (error) {
          console.error("删除地址失败:", error);
          uni.showToast({
            title: "删除失败，请重试",
            icon: "none",
          });
        }
      }
    },
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
/* 表单输入框 */
.form-input-wrapper {
  position: relative;
}

.form-input {
  height: 88rpx;
  background: var(--bg-input);
  border-radius: var(--radius-2);
  padding: 0 24rpx;
  font-size: 28rpx;
  color: var(--text-base);
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: var(--primary);
  background: var(--bg-card);
}

/* tui-textarea 相关样式已由组件内部处理，移除重复样式 */

/* tui-textarea focus 状态也由组件内部处理 */

/* 字符计数样式已由 tui-textarea 组件内部处理 */

/* 上传区域 */
.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.upload-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: var(--radius-2);
  overflow: hidden;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: var(--radius-2);
}

.delete-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed var(--border-light);
  border-radius: var(--radius-2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--bg-input);
  transition: all 0.3s ease;
}

.upload-btn:active {
  transform: scale(0.98);
  background: var(--bg-search);
}

.upload-hint {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
}

/* switch组件容器样式 */
.switch-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 底部按钮 */
.action-buttons {
  display: flex;
  gap: 24rpx;
}

/* 单按钮布局 - 新增模式 */
.action-buttons.single-button .save-btn {
  flex: 1;
  width: auto;
}

/* 删除按钮 - 使用全局text-red样式 */
.delete-btn {
  flex: 1;
  height: 88rpx;
  background: transparent;
  border: 2rpx solid var(--color-red);
  border-radius: var(--radius-2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.delete-btn:active {
  transform: scale(0.98);
  background: var(--bg-red-light);
}

.delete-text {
  font-size: 32rpx;
  font-weight: 500;
}

.save-btn {
  flex: 2;
  height: 88rpx;
  background: var(--primary);
  border-radius: var(--radius-2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.save-btn.disabled {
  background: rgba(var(--primary), 0.5);
}

.save-text {
  font-size: 32rpx;
  color: white;
  font-weight: 500;
}

.save-btn.disabled .save-text {
  color: var(--text-disable);
}
</style>
