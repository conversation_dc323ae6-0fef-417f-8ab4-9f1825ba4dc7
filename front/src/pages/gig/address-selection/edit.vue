<template>
  <view class="container pb-180rpx">
    <!-- 表单内容 -->
    <!-- 地址标题 -->
    <Card title="工作地点">
      <view class="form-input-wrapper">
        <input
          class="form-input"
          v-model="formData.name"
          placeholder="请输入地址标题，如：公司、家等"
          maxlength="20"
        />
      </view>
    </Card>

    <!-- 详细地址 -->
    <Card title="详细地址">
      <tui-textarea
        v-model="formData.fullAddress"
        placeholder="请补充路号+详细门牌号，例如南京东路888号1层101室右拐"
        :maxlength="150"
        minHeight="120rpx"
        :isCounter="true"
        height="100rpx"
        backgroundColor="var(--bg-input)"
        :borderTop="false"
        :borderBottom="false"
      ></tui-textarea>
    </Card>

    <!-- 找路指引 -->
    <Card title="找路指引（上传门头照，非必填）">
      <view class="guide-section">
        <view class="guide-description mb-24rpx">
          <input
            v-model="formData.navigationGuide"
            placeholder="例如进大门右拐上楼，请勿填写手机或微信号"
            :maxlength="50"
            backgroundColor="var(--bg-input)"
          />
        </view>

        <!-- 上传区域 -->
        <view class="upload-section">
          <view class="upload-grid">
            <!-- 已上传的图片 -->
            <view
              v-for="(image, index) in formData.guideImages"
              :key="index"
              class="upload-item"
            >
              <image :src="image" class="upload-image" mode="aspectFill" />
              <view class="delete-btn" @click="removeImage(index)">
                <view
                  class="i-solar:close-circle-bold text-32rpx text-white"
                ></view>
              </view>
            </view>

            <!-- 上传视频 -->
            <view
              v-for="(video, index) in formData.guideVideos"
              :key="index"
              class="upload-item video-item"
            >
              <video :src="video" class="upload-video" />
              <view class="video-overlay">
                <view
                  class="i-solar:play-circle-bold text-64rpx text-white opacity-80"
                ></view>
              </view>
              <view class="delete-btn" @click="removeVideo(index)">
                <view
                  class="i-solar:close-circle-bold text-32rpx text-white"
                ></view>
              </view>
            </view>

            <!-- 上传按钮 -->
            <view
              v-if="
                formData.guideImages.length + formData.guideVideos.length < 6
              "
              class="upload-btn"
              @click="showUploadOptions"
            >
              <view
                class="i-solar:camera-bold text-64rpx text-secondary mb-12rpx"
              ></view>
              <text class="text-info text-24rpx">上传照片</text>
            </view>
          </view>

          <view class="upload-hint">
            <text class="text-info text-24rpx"
              >地点不好找，建议上传照片或视频</text
            >
            <text class="text-primary text-24rpx ml-16rpx" @click="showExample"
              >查看示例</text
            >
          </view>
        </view>
      </view>
    </Card>

    <!-- 设为默认 -->
    <Card :margin="'24rpx 24rpx 0 24rpx'">
      <view class="flex-x-between" @click="toggleDefault">
        <view class="flex-1">
          <view class="text-base text-32rpx font-weight-500 mb-8rpx"
            >设为默认地址</view
          >
          <view class="text-info text-26rpx">默认地址会优先显示在列表顶部</view>
        </view>
        <view class="switch-wrapper ml-24rpx">
          <view class="switch" :class="{ active: formData.isDefault }">
            <view class="switch-thumb"></view>
          </view>
        </view>
      </view>
    </Card>

    <!-- 底部按钮 -->
    <BottomActionBar>
      <view class="action-buttons">
        <view class="cancel-btn" @click="goBack">
          <text class="cancel-text">删除</text>
        </view>
        <view
          class="save-btn"
          @click="saveAddress"
          :class="{ disabled: !canSave }"
        >
          <text class="save-text">保存</text>
        </view>
      </view>
    </BottomActionBar>
  </view>
</template>

<script setup lang="ts">
import BottomActionBar from "@/components/common/BottomActionBar.vue";
import Card from "@/components/common/Card.vue";
import tuiTextarea from "@/components/thorui/tui-textarea/tui-textarea.vue"
import type { CreateAddressRequest, UpdateAddressRequest } from "@/types/user";
import UserApi from "@/api/user";
import { callApi, submitForm } from "@/utils/network/helpers";

interface AddressForm {
  id?: number;
  name: string;
  address: string;
  detailAddress: string;
  fullAddress: string;
  latitude: number;
  longitude: number;
  navigationGuide: string;
  guideImages: string[];
  guideVideos: string[];
  isDefault: boolean;
}

// 页面参数
const mode = ref("add"); // 'add' 或 'edit'
const addressId = ref(0);
const isEdit = computed(() => mode.value === "edit");

// 表单数据
const formData = ref<AddressForm>({
  name: "",
  address: "",
  detailAddress: "",
  fullAddress: "",
  latitude: 0,
  longitude: 0,
  navigationGuide: "",
  guideImages: [],
  guideVideos: [],
  isDefault: false,
});

// 计算是否可以保存
const canSave = computed(() => {
  return formData.value.name.trim() && formData.value.fullAddress.trim();
});

// 页面加载时处理参数
onLoad((options: any) => {
  mode.value = options.mode || "add";

  if (mode.value === "edit" && options.id) {
    addressId.value = parseInt(options.id);
    formData.value.name = decodeURIComponent(options.name || "");
    formData.value.fullAddress = decodeURIComponent(options.fullAddress || "");
    formData.value.navigationGuide = decodeURIComponent(
      options.navigationGuide || ""
    );
    formData.value.isDefault = options.isDefault === "true";
    formData.value.id = parseInt(options.id);

    // 如果是编辑模式，加载详细信息
    loadAddressDetail();
  }
});

// 加载地址详情
const loadAddressDetail = async () => {
  const result = await callApi(() => UserApi.getAddressDetail(addressId.value));
  if (result.success && result.data?.data) {
    const address = result.data.data;
    formData.value = {
      id: address.id,
      name: address.name,
      address: address.address,
      detailAddress: address.detailAddress,
      fullAddress: address.fullAddress,
      latitude: address.latitude,
      longitude: address.longitude,
      navigationGuide: address.navigationGuide,
      guideImages: address.guideImages || [],
      guideVideos: address.guideVideos || [],
      isDefault: address.isDefault,
    };
  }
};

// 切换默认状态
const toggleDefault = () => {
  formData.value.isDefault = !formData.value.isDefault;
};

// 显示上传选项
const showUploadOptions = () => {
  uni.showActionSheet({
    itemList: ["拍照", "从相册选择", "录制视频"],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          chooseImage("camera");
          break;
        case 1:
          chooseImage("album");
          break;
        case 2:
          chooseVideo();
          break;
      }
    },
  });
};

// 选择图片
const chooseImage = (sourceType: "camera" | "album") => {
  uni.chooseImage({
    count:
      6 - formData.value.guideImages.length - formData.value.guideVideos.length,
    sizeType: ["compressed"],
    sourceType: [sourceType],
    success: (res) => {
      // TODO: 上传到服务器
      formData.value.guideImages.push(...res.tempFilePaths);
    },
  });
};

// 选择视频
const chooseVideo = () => {
  uni.chooseVideo({
    sourceType: ["camera", "album"],
    maxDuration: 60,
    camera: "back",
    success: (res) => {
      // TODO: 上传到服务器
      formData.value.guideVideos.push(res.tempFilePath);
    },
  });
};

// 删除图片
const removeImage = (index: number) => {
  formData.value.guideImages.splice(index, 1);
};

// 删除视频
const removeVideo = (index: number) => {
  formData.value.guideVideos.splice(index, 1);
};

// 显示示例
const showExample = () => {
  uni.showModal({
    title: "找路指引示例",
    content: "例如：进大门后右转，上楼梯到二楼，找到201房间门口等候",
    showCancel: false,
  });
};

// 保存地址
const saveAddress = async () => {
  if (!canSave.value) {
    return;
  }

  if (mode.value === "add") {
    const createData: CreateAddressRequest = {
      name: formData.value.name,
      address: formData.value.address,
      detailAddress: formData.value.detailAddress,
      fullAddress: formData.value.fullAddress,
      latitude: formData.value.latitude,
      longitude: formData.value.longitude,
      navigationGuide: formData.value.navigationGuide,
      guideImages: formData.value.guideImages,
      guideVideos: formData.value.guideVideos,
    };

    await submitForm(() => UserApi.createAddress(createData), {
      loadingText: "创建中...",
      successText: "新增成功",
      onSuccess: () => {
        setTimeout(() => goBack(), 1500);
      },
    });
  } else {
    const updateData: UpdateAddressRequest = {
      name: formData.value.name,
      address: formData.value.address,
      detailAddress: formData.value.detailAddress,
      fullAddress: formData.value.fullAddress,
      latitude: formData.value.latitude,
      longitude: formData.value.longitude,
      navigationGuide: formData.value.navigationGuide,
      guideImages: formData.value.guideImages,
      guideVideos: formData.value.guideVideos,
    };

    const result = await submitForm(
      () => UserApi.updateAddress(addressId.value, updateData),
      {
        loadingText: "保存中...",
        successText: "保存成功",
      }
    );

    if (result.success) {
      // 如果设置为默认地址，需要额外调用设置默认的API
      if (formData.value.isDefault) {
        await callApi(() => UserApi.setDefaultAddress(addressId.value));
      }

      setTimeout(() => goBack(), 1500);
    }
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
/* 表单输入框 */
.form-input-wrapper {
  position: relative;
}

.form-input {
  height: 88rpx;
  background: var(--bg-input);
  border-radius: var(--radius-2);
  padding: 0 24rpx;
  font-size: 28rpx;
  color: var(--text-base);
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: var(--primary);
  background: var(--bg-card);
}

/* tui-textarea 相关样式已由组件内部处理，移除重复样式 */

/* tui-textarea focus 状态也由组件内部处理 */

/* 字符计数样式已由 tui-textarea 组件内部处理 */

/* 上传区域 */
.upload-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.upload-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: var(--radius-2);
  overflow: hidden;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: var(--radius-2);
}

.upload-video {
  width: 100%;
  height: 100%;
  border-radius: var(--radius-2);
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
}

.delete-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed var(--border-light);
  border-radius: var(--radius-2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--bg-input);
  transition: all 0.3s ease;
}

.upload-btn:active {
  transform: scale(0.98);
  background: var(--bg-search);
}

.upload-hint {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
}

/* 开关样式 */
.switch-wrapper {
  padding: 8rpx;
}

.switch {
  width: 96rpx;
  height: 56rpx;
  background: var(--bg-gray);
  border-radius: 28rpx;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
}

.switch.active {
  background: var(--primary);
}

.switch-thumb {
  width: 48rpx;
  height: 48rpx;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.switch.active .switch-thumb {
  transform: translateX(40rpx);
}

/* 底部按钮 */
.action-buttons {
  display: flex;
  gap: 24rpx;
}

.cancel-btn {
  flex: 1;
  height: 88rpx;
  background: var(--bg-search);
  border-radius: var(--radius-2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.cancel-btn:active {
  transform: scale(0.98);
  background: var(--bg-tag);
}

.cancel-text {
  font-size: 32rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

.save-btn {
  flex: 2;
  height: 88rpx;
  background: var(--primary);
  border-radius: var(--radius-2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.save-btn:active:not(.disabled) {
  transform: scale(0.98);
  background: var(--primary-700);
}

.save-btn.disabled {
  background: var(--bg-disable);
  cursor: not-allowed;
}

.save-text {
  font-size: 32rpx;
  color: white;
  font-weight: 500;
}

.save-btn.disabled .save-text {
  color: var(--text-disable);
}
</style>
