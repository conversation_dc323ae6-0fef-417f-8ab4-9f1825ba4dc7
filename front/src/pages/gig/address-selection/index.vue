<template>
  <view class="container pb-180rpx">
    <!-- 地址列表 -->
    <view class="content">
      <!-- 地址卡片 - 自定义实现选中效果 -->
      <view
        v-for="address in addressList"
        :key="address.id"
        class="card"
        :class="{ selected: selectedAddressId === address.id }"
        @click="selectAddress(address)"
      >
        <!-- 地址内容 -->
        <view class="address-content">
          <!-- 地址标题行 -->
          <view class="address-header">
            <text class="address-title">{{ address.name }}</text>
          </view>

          <!-- 地址详情 -->
          <view class="address-detail">
            {{ address.full_address }}
          </view>

          <!-- 底部操作栏 -->
          <view class="address-actions">
            <!-- 左侧：设置默认复选框 -->
            <label
              class="default-setting"
              @click.stop="setDefaultAddress(address)"
            >
              <checkbox
                :checked="address.is_default"
                class="scale-80"
                color="var(--text-grey)"
                @click.stop
              />
              <text class="checkbox-label">{{
                address.is_default ? "已默认" : "设置默认"
              }}</text>
            </label>

            <!-- 右侧：操作按钮 -->
            <view class="action-buttons">
              <view
                class="action-btn delete-btn"
                @click.stop="deleteAddress(address.id)"
              >
                <text class="btn-text text-red">删除</text>
              </view>
              <view
                class="action-btn edit-btn"
                @click.stop="editAddress(address)"
              >
                <text class="btn-text">修改</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view
        v-if="addressList.length === 0"
        class="flex-y-center text-center pt-120rpx"
      >
        <view
          class="i-solar:map-point-favourite-linear text-120rpx text-gray opacity-30 mb-32rpx"
        ></view>
        <view class="text-secondary text-32rpx font-weight-500 mb-16rpx">
          暂无招工地址
        </view>
        <view class="text-info text-28rpx leading-1.4">
          添加常用的招工地址，方便快速选择
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <BottomActionBar>
      <view class="w-full">
        <view
          class="w-full h-88rpx bg-primary rounded-16rpx flex-y-center interactive-scale"
          @click="addAddress"
        >
          <text class="text-white text-32rpx font-weight-500"
            >新增招工地址</text
          >
        </view>
      </view>
    </BottomActionBar>
  </view>
</template>

<script setup lang="ts">
import BottomActionBar from "@/components/common/BottomActionBar.vue";
import type { UserAddress } from "@/types/user";
import UserApi from "@/api/user";
import { callApi } from "@/utils/network/helpers";

interface Emits {
  (e: "select", address: UserAddress): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const selectedAddressId = ref<number>(0);
const addressList = ref<UserAddress[]>([]);

// 页面加载时获取地址列表
onMounted(() => {
  loadAddressList();
});

// 加载地址列表
const loadAddressList = async () => {
  const result = await callApi(() => UserApi.getAddressList(), {
    showLoading: true,
  });
  if (result.success) {
    addressList.value = result.data;
  }
};

// 选择地址
const selectAddress = (address: UserAddress) => {
  selectedAddressId.value = address.id;

  // 显示选中效果后返回
  setTimeout(() => {
    // 通过全局事件传递选中的地址信息
    uni.$emit("addressSelected", {
      id: address.id,
      name: address.name,
      fullAddress: address.full_address,
      detailAddress: address.detail_address || "",
      latitude: address.latitude,
      longitude: address.longitude,
      isDefault: address.is_default,
    });

    // 发送emit事件（兼容性）
    emit("select", address);

    // 返回上一页
    goBack();
  }, 200);
};

// 设置默认地址
const setDefaultAddress = async (address: UserAddress) => {
  if (address.is_default) return;

  const result = await callApi(() => UserApi.setDefaultAddress(address.id), {
    showSuccessToast: true,
    successText: "设置成功",
  });

  if (result.success) {
    // 更新本地状态
    addressList.value.forEach((item) => {
      item.is_default = item.id === address.id;
    });
  }
};

// 新增地址
const addAddress = () => {
  uni.navigateTo({
    url: "/pages/gig/address-selection/edit?mode=add",
  });
};

// 编辑地址
const editAddress = (address: UserAddress) => {
  uni.navigateTo({
    url: `/pages/gig/address-selection/edit?mode=edit&id=${
      address.id
    }&name=${encodeURIComponent(address.name)}&fullAddress=${encodeURIComponent(
      address.full_address
    )}&is_default=${address.is_default}&navigation_guide=${encodeURIComponent(
      address.navigation_guide || ""
    )}`,
  });
};

// 删除地址
const deleteAddress = (addressId: number) => {
  uni.showModal({
    title: "确认删除",
    content: "确定要删除这个地址吗？",
    success: async (res) => {
      if (res.confirm) {
        const result = await callApi(() => UserApi.deleteAddress(addressId), {
          showSuccessToast: true,
          successText: "删除成功",
        });

        if (result.success) {
          // 更新本地状态
          addressList.value = addressList.value.filter(
            (item) => item.id !== addressId
          );
        }
      }
    },
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: var(--bg-page);
}

.content {
  padding: 10rpx;
}

// 地址卡片 - 参考图片设计
.card {
  overflow: hidden;
}

// 选中状态 - 使用主题色
.selected {
  border: 1rpx solid var(--primary);
  background-color: rgba(var(--bg-primary-light), 0.5);
}

// 地址内容区域
.address-content {
  width: 100%;
}

// 地址标题行
.address-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.address-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
  line-height: 1.3;
  flex: 1;
}

// 默认标签
.default-tag {
  background: var(--primary);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-left: 16rpx;
}

.default-text {
  font-size: 22rpx;
  color: var(--text-base);
  font-weight: 500;
}

// 地址详情
.address-detail {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 24rpx;
  word-break: break-all;
}

// 底部操作栏
.address-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 20rpx;
  border-top: 1rpx solid var(--border-light);
}

// 左侧：设置默认
.default-setting {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.checkbox-label {
  font-size: 28rpx;
  color: var(--text-info);
  user-select: none;
}

// 右侧：操作按钮
.action-buttons {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.action-btn {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  min-width: 60rpx;
  text-align: center;
}

.action-btn:active {
  background: var(--bg-search);
  transform: scale(0.95);
}

.btn-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 500;
}
</style>
