<template>
  <view class="container pb-180rpx">
    <!-- 地址列表 -->
    <view class="content">
      <Card 
        v-for="address in addressList" 
        :key="address.id"
        :margin="'24rpx 24rpx 0 24rpx'"
        :is-shadow="true"
        class="interactive-scale"
        :class="{ 'border-primary': selectedAddressId === address.id }"
        @click="selectAddress(address)"
      >
        <!-- 地址信息区域 -->
        <view class="flex-x-between items-start">
          <!-- 地址内容 -->
          <view class="flex-1 pr-24rpx">
            <view class="flex-x-center gap-16rpx mb-12rpx">
              <!-- 选中状态图标 -->
              <view v-if="selectedAddressId === address.id" class="text-primary">
                <view class="i-solar:check-circle-bold text-48rpx"></view>
              </view>
              <text class="text-base font-weight-500 text-32rpx">{{ address.name }}</text>
              <view v-if="address.isDefault" class="tag tag-primary tag-sm">默认</view>
            </view>
            <view class="text-secondary text-28rpx leading-1.4 mb-8rpx">
              {{ address.fullAddress }}
            </view>
            <view v-if="address.navigationGuide" class="text-info text-26rpx">
              <view class="i-solar:map-point-favourite-outline inline-block mr-8rpx"></view>
              {{ address.navigationGuide }}
            </view>
          </view>
          
          <!-- 操作按钮 - 右侧垂直排列 -->
          <view class="flex-y-center gap-16rpx">
            <view class="action-btn-icon" @click.stop="editAddress(address)">
              <view class="i-solar:pen-bold text-40rpx text-gray"></view>
            </view>
            <view class="action-btn-icon" @click.stop="deleteAddress(address.id)">
              <view class="i-solar:trash-bin-minimalistic-bold text-40rpx text-gray"></view>
            </view>
          </view>
        </view>
        
        <!-- 分割线 -->
        <view class="divider my-24rpx"></view>
        
        <!-- 底部操作 -->
        <view class="flex-x-between">
          <view class="flex-x-center gap-12rpx" @click.stop="setDefaultAddress(address)">
            <view class="switch-wrapper">
              <view 
                class="w-32rpx h-32rpx rounded-full border-2rpx flex-y-center"
                :class="address.isDefault ? 'bg-primary border-primary' : 'border-gray'"
              >
                <view v-if="address.isDefault" class="i-solar:check-bold text-16rpx text-white mx-auto"></view>
              </view>
            </view>
            <text class="text-secondary text-26rpx">设为默认地址</text>
          </view>
          
          <view class="text-info text-24rpx" v-if="address.lastUsedAt">
            最近使用: {{ formatRelativeTime(address.lastUsedAt) }}
          </view>
        </view>
      </Card>
      
      <!-- 空状态 -->
      <view v-if="addressList.length === 0" class="flex-y-center text-center pt-120rpx">
        <view class="i-solar:map-point-favourite-linear text-120rpx text-gray opacity-30 mb-32rpx"></view>
        <view class="text-secondary text-32rpx font-weight-500 mb-16rpx">
          暂无招工地址
        </view>
        <view class="text-info text-28rpx leading-1.4">
          添加常用的招工地址，方便快速选择
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <BottomActionBar>
      <view class="w-full">
        <view 
          class="w-full h-88rpx bg-primary rounded-16rpx flex-y-center interactive-scale"
          @click="addAddress"
        >
          <text class="text-white text-32rpx font-weight-500">新增招工地址</text>
        </view>
      </view>
    </BottomActionBar>
  </view>
</template>

<script setup lang="ts">
import BottomActionBar from "@/components/common/BottomActionBar.vue";
import Card from "@/components/common/Card.vue";
import type { UserAddress } from "@/types/user";
import { formatRelativeTime } from "@/utils/core/date";
import UserApi from "@/api/user";
import { callApi, submitForm } from "@/utils/network/helpers";

interface Emits {
  (e: "select", address: UserAddress): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const selectedAddressId = ref<number>(0);
const addressList = ref<UserAddress[]>([
  {
    id: 1,
    name: "金田影视传媒产业园-12号楼",
    address: "北京市朝阳区金田影视传媒产业园",
    detailAddress: "1单元门口",
    fullAddress: "北京市朝阳区金田影视传媒产业园1单元门口",
    latitude: 39.9042,
    longitude: 116.4074,
    navigationGuide: "零工将导航到此地点",
    guideImages: [],
    guideVideos: [],
    isDefault: true,
    lastUsedAt: "2024-08-04T10:30:00Z",
    createdAt: "2024-08-01T10:00:00Z",
    updatedAt: "2024-08-04T10:30:00Z",
  },
  {
    id: 2,
    name: "望京SOHO",
    address: "北京市朝阳区望京街10号",
    detailAddress: "望京SOHO塔1A座",
    fullAddress: "北京市朝阳区望京街10号望京SOHO塔1A座",
    latitude: 39.9965,
    longitude: 116.4778,
    navigationGuide: "",
    guideImages: [],
    guideVideos: [],
    isDefault: false,
    createdAt: "2024-08-02T10:00:00Z",
    updatedAt: "2024-08-02T10:00:00Z",
  },
]);

// 页面加载时获取地址列表
onMounted(() => {
  loadAddressList();
});

// 加载地址列表
const loadAddressList = async () => {
  const result = await callApi(() => UserApi.getAddressList());
  if (result.success && result.data?.data?.addresses) {
    addressList.value = result.data.data.addresses;
  }
};

// 选择地址
const selectAddress = (address: UserAddress) => {
  selectedAddressId.value = address.id;
  
  const pages = getCurrentPages();
  const prevPage = pages[pages.length - 2];
  
  if (prevPage && prevPage.route?.includes('basic-info')) {
    prevPage.$vm.updateLocation(address);
  }
  
  setTimeout(() => {
    emit("select", address);
    goBack();
  }, 200);
};

// 设置默认地址
const setDefaultAddress = async (address: UserAddress) => {
  if (address.isDefault) return;

  const result = await callApi(() => UserApi.setDefaultAddress(address.id), {
    showSuccessToast: true,
    successText: '设置成功'
  });
  
  if (result.success) {
    // 更新本地状态
    addressList.value.forEach(item => {
      item.isDefault = item.id === address.id;
    });
  }
};

// 新增地址
const addAddress = () => {
  uni.navigateTo({
    url: "/pages/gig/address-selection/edit?mode=add",
  });
};

// 编辑地址
const editAddress = (address: UserAddress) => {
  uni.navigateTo({
    url: `/pages/gig/address-selection/edit?mode=edit&id=${address.id}&name=${encodeURIComponent(address.name)}&fullAddress=${encodeURIComponent(address.fullAddress)}&isDefault=${address.isDefault}&navigationGuide=${encodeURIComponent(address.navigationGuide || '')}`,
  });
};

// 删除地址
const deleteAddress = (addressId: number) => {
  uni.showModal({
    title: "确认删除",
    content: "确定要删除这个地址吗？",
    success: async (res) => {
      if (res.confirm) {
        const result = await callApi(() => UserApi.deleteAddress(addressId), {
          showSuccessToast: true,
          successText: '删除成功'
        });
        
        if (result.success) {
          // 更新本地状态
          addressList.value = addressList.value.filter(item => item.id !== addressId);
        }
      }
    },
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.content{
  padding: 0;
  margin: 0;
}
.border-primary {
  border: 2rpx solid var(--primary) !important;
}

.action-btn-icon {
  padding: 12rpx;
  border-radius: 8rpx;
  transition: all 0.2s ease;
  background: transparent;
}

.action-btn-icon:active {
  transform: scale(0.95);
  background: var(--bg-hover);
}

.action-btn {
  padding: 8rpx;
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: scale(0.95);
}
</style>