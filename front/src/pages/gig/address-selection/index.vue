<template>
  <view class="container">

    <!-- 地址列表 -->
    <view class="address-list">
      <view 
        v-for="(address, index) in addressList" 
        :key="address.id"
        class="address-item"
        :class="{ 'selected': selectedAddressId === address.id }"
        @click="selectAddress(address)"
      >
        <!-- 地址卡片 -->
        <view class="address-card">
          <!-- 选中状态图标 -->
          <view class="address-icon">
            <view class="icon-wrapper" :class="{ 'selected': selectedAddressId === address.id }">
              <text class="checkmark" v-if="selectedAddressId === address.id">✓</text>
            </view>
          </view>
          
          <!-- 地址信息 -->
          <view class="address-info">
            <view class="address-title">
              <text class="title-text">{{ address.title }}</text>
              <view class="default-badge" v-if="address.isDefault">
                <text class="badge-text">默认</text>
              </view>
            </view>
            <view class="address-detail">
              <text class="detail-text">{{ address.detail }}</text>
            </view>
          </view>
          
          <!-- 操作按钮 -->
          <view class="address-actions">
            <view class="action-btn" @click.stop="deleteAddress(address.id)">
              <text class="action-text">删除</text>
            </view>
            <view class="action-btn primary" @click.stop="editAddress(address)">
              <text class="action-text">修改</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-if="addressList.length === 0" class="empty-state">
        <view class="empty-icon">
          <text class="icon-location">📍</text>
        </view>
        <view class="empty-text">
          <text>暂无招工地址</text>
        </view>
        <view class="empty-desc">
          <text>添加常用的招工地址，方便快速选择</text>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <BottomActionBar>
      <view class="bottom-actions">
        <view class="add-btn" @click="addAddress">
          <text class="btn-text">新增招工地址</text>
        </view>
      </view>
    </BottomActionBar>
  </view>
</template>

<script setup lang="ts">
import BottomActionBar from "@/components/common/BottomActionBar.vue";

interface Address {
  id: string;
  title: string;
  detail: string;
  isDefault: boolean;
}

interface Emits {
  (e: "select", address: Address): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const selectedAddressId = ref<string>("");
const addressList = ref<Address[]>([
  {
    id: "1",
    title: "金田影视传媒产业园-12号楼",
    detail: "北京市朝阳区金田影视传媒产业园1单元门口",
    isDefault: true,
  },
  {
    id: "2",
    title: "望京SOHO",
    detail: "北京市朝阳区望京街10号望京SOHO塔1A座",
    isDefault: false,
  },
]);

// 页面加载时获取地址列表
onMounted(() => {
  loadAddressList();
});

// 加载地址列表
const loadAddressList = async () => {
  try {
    // TODO: 调用API获取地址列表
    console.log("加载地址列表");
  } catch (error) {
    console.error("加载地址列表失败:", error);
  }
};

// 选择地址
const selectAddress = (address: Address) => {
  selectedAddressId.value = address.id;
  
  // 获取当前页面栈
  const pages = getCurrentPages();
  const prevPage = pages[pages.length - 2];
  
  // 如果上一页是basic-info页面，调用其updateLocation方法
  if (prevPage && prevPage.route?.includes('basic-info')) {
    prevPage.$vm.updateLocation(address);
  }
  
  // 延迟一下再返回，让用户看到选中效果
  setTimeout(() => {
    emit("select", address);
    goBack();
  }, 200);
};

// 新增地址
const addAddress = () => {
  uni.navigateTo({
    url: "/pages/gig/address-selection/edit?mode=add",
  });
};

// 编辑地址
const editAddress = (address: Address) => {
  uni.navigateTo({
    url: `/pages/gig/address-selection/edit?mode=edit&id=${address.id}&title=${encodeURIComponent(address.title)}&detail=${encodeURIComponent(address.detail)}&isDefault=${address.isDefault}`,
  });
};

// 删除地址
const deleteAddress = (addressId: string) => {
  uni.showModal({
    title: "确认删除",
    content: "确定要删除这个地址吗？",
    success: (res) => {
      if (res.confirm) {
        addressList.value = addressList.value.filter(item => item.id !== addressId);
        uni.showToast({
          title: "删除成功",
          icon: "success",
        });
      }
    },
  });
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: var(--bg-page);
  padding-bottom: 140rpx;
}


/* 地址列表 */
.address-list {
  padding: 24rpx;
  margin-top: calc(88rpx + var(--status-bar-height, 44rpx));
}

.address-item {
  margin-bottom: 24rpx;
  transition: all 0.3s ease;
}

.address-item.selected .address-card {
  border-color: var(--primary);
  background: var(--bg-primary-light);
}

.address-card {
  background: var(--bg-card);
  border-radius: var(--radius-3);
  padding: 32rpx 24rpx;
  border: 2rpx solid var(--border-light);
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 8rpx rgba(195, 195, 210, 0.1);
}

.address-card:active {
  transform: scale(0.98);
}

/* 选中图标 */
.address-icon {
  margin-top: 8rpx;
}

.icon-wrapper {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.icon-wrapper.selected {
  background: var(--primary);
  border-color: var(--primary);
}

.checkmark {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

/* 地址信息 */
.address-info {
  flex: 1;
  min-width: 0;
}

.address-title {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-base);
  flex: 1;
}

.default-badge {
  background: var(--primary);
  border-radius: var(--radius-1);
  padding: 4rpx 12rpx;
}

.badge-text {
  font-size: 20rpx;
  color: white;
  font-weight: 500;
}

.address-detail {
  margin-bottom: 8rpx;
}

.detail-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 操作按钮 */
.address-actions {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-top: 8rpx;
}

.action-btn {
  padding: 8rpx 16rpx;
  border-radius: var(--radius-1);
  background: var(--bg-search);
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: var(--primary);
}

.action-btn:active {
  transform: scale(0.95);
}

.action-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  text-align: center;
}

.action-btn.primary .action-text {
  color: white;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  margin-bottom: 32rpx;
}

.icon-location {
  font-size: 120rpx;
  opacity: 0.3;
}

.empty-text {
  margin-bottom: 16rpx;
}

.empty-text text {
  font-size: 32rpx;
  color: var(--text-secondary);
  font-weight: 500;
}

.empty-desc text {
  font-size: 28rpx;
  color: var(--text-info);
  line-height: 1.4;
}

/* 底部按钮 */
.bottom-actions {
  width: 100%;
}

.add-btn {
  width: 100%;
  height: 88rpx;
  background: var(--primary);
  border-radius: var(--radius-2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.add-btn:active {
  transform: scale(0.98);
  background: var(--primary-700);
}

.btn-text {
  font-size: 32rpx;
  color: white;
  font-weight: 500;
}
</style>