<template>
  <view class="container">
    <uni-nav-bar
      fixed
      left-icon="back"
      :statusBar="true"
      :border="false"
      @clickLeft="goBack"
    >
      <template #default>
        <StepNavigator :current-step="currentStep" />
      </template>
    </uni-nav-bar>

    <!-- 页面内容 -->
    <view class="content">
      <!-- 显示当前步骤的内容 -->
      <BasicInfo v-if="currentStep === 1" @next="nextStep" />
      <Requirements
        v-if="currentStep === 2"
        @prev="prevStep"
        @publish="publishGig"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import BasicInfo from "./basic-info.vue";
import Requirements from "./requirements.vue";
import StepNavigator from "./components/StepNavigator.vue";

// 当前步骤
const currentStep = ref(1);

// 导航函数
const goBack = () => {
  uni.navigateBack();
};

// 步骤控制
const nextStep = () => {
  if (currentStep.value < 2) {
    currentStep.value++;
  }
};

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--;
  }
};

const publishGig = () => {
  console.log("发布零工");
  // 这里处理发布逻辑
};
</script>

<style lang="scss" scoped>
.container {
  height: 100vh;
  background-color: var(--bg-page);
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
}
</style>
