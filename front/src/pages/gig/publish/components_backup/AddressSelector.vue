<template>
  <view class="address-selector">
    <!-- 已保存的地址列表 -->
    <view v-if="savedAddresses.length > 0" class="saved-addresses">
      <text class="section-title">常用地址</text>
      <view class="address-list">
        <view
          v-for="addr in savedAddresses"
          :key="addr.id"
          class="address-item"
          :class="{ active: selectedAddressId === addr.id }"
          @click="selectSavedAddress(addr)"
        >
          <view class="address-info">
            <text class="address-name">{{ addr.name }}</text>
            <text class="address-detail">{{ addr.full_address }}</text>
          </view>
          <view class="address-meta">
            <text v-if="addr.is_default" class="default-tag">默认</text>
            <text class="usage-count">已用{{ addr.usage_count }}次</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 手动输入地址 -->
    <view class="input-section">
      <text class="section-title">{{ savedAddresses.length > 0 ? '或重新输入地址' : '输入工作地址' }}</text>
      
      <!-- 地点名称 -->
      <view class="input-row">
        <text class="input-label">地点名称</text>
        <input
          v-model="localAddress.addressName"
          placeholder="如：XX公司、XX工厂"
          class="address-input"
          @input="onAddressChange"
        />
      </view>

      <!-- 详细地址 -->
      <view class="input-row">
        <text class="input-label">详细地址</text>
        <textarea
          v-model="localAddress.detailAddress"
          placeholder="请输入详细地址"
          class="address-textarea"
          maxlength="200"
          @input="onAddressChange"
        />
      </view>

      <!-- 位置选择按钮 -->
      <view class="location-actions">
        <view class="action-btn" @click="getCurrentLocation">
          <text class="action-icon">📍</text>
          <text class="action-text">获取当前位置</text>
        </view>
        <view class="action-btn" @click="openMapSelector">
          <text class="action-icon">🗺️</text>
          <text class="action-text">地图选择</text>
        </view>
      </view>

      <!-- 当前选中的位置信息 -->
      <view v-if="localAddress.address" class="selected-location">
        <text class="location-text">{{ localAddress.address }}</text>
      </view>
    </view>

    <!-- 保存地址选项 -->
    <view v-if="!selectedAddressId && (localAddress.address || localAddress.addressName)" class="save-option">
      <view class="save-row" @click="toggleSaveAddress">
        <text class="save-label">保存为常用地址</text>
        <view class="checkbox" :class="{ checked: shouldSaveAddress }">
          <text v-if="shouldSaveAddress" class="check-icon">✓</text>
        </view>
      </view>
      
      <view v-if="shouldSaveAddress" class="save-name-input">
        <input
          v-model="saveAddressName"
          placeholder="输入地址别名，如：公司、工厂A"
          class="address-input"
          maxlength="20"
        />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import type { UserAddress } from '@/types/gig'
import { showToast } from '@/utils/ui/feedback'

interface Props {
  address: string
  detailAddress?: string
  addressName?: string
  latitude: number
  longitude: number
}

interface Emits {
  (e: 'update:address', data: {
    address: string
    detail_address?: string
    address_name?: string
    latitude: number
    longitude: number
  }): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 本地地址数据
const localAddress = reactive({
  address: props.address || '',
  detailAddress: props.detailAddress || '',
  addressName: props.addressName || '',
  latitude: props.latitude || 0,
  longitude: props.longitude || 0
})

// 保存的地址列表
const savedAddresses = ref<UserAddress[]>([])
const selectedAddressId = ref<number | null>(null)

// 保存地址选项
const shouldSaveAddress = ref(false)
const saveAddressName = ref('')

// 加载保存的地址
onMounted(async () => {
  await loadSavedAddresses()
})

// 加载用户保存的地址
const loadSavedAddresses = async () => {
  try {
    // TODO: 调用API获取用户保存的地址
    // const addresses = await getUserAddresses()
    // savedAddresses.value = addresses
    
    // 模拟数据
    savedAddresses.value = [
      {
        id: 1,
        user_id: 1,
        name: '公司',
        address: '浙江省杭州市西湖区',
        detail_address: '文三路268号华星科技大厦A座',
        full_address: '浙江省杭州市西湖区文三路268号华星科技大厦A座',
        latitude: 30.2741,
        longitude: 120.1551,
        is_default: true,
        usage_count: 15,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        user_id: 1,
        name: '工厂A',
        address: '浙江省杭州市萧山区',
        detail_address: '经济技术开发区建设一路88号',
        full_address: '浙江省杭州市萧山区经济技术开发区建设一路88号',
        latitude: 30.1649,
        longitude: 120.2695,
        is_default: false,
        usage_count: 8,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }
    ]
  } catch (error) {
    console.error('加载地址失败:', error)
  }
}

// 选择已保存的地址
const selectSavedAddress = async (address: UserAddress) => {
  selectedAddressId.value = address.id
  
  localAddress.address = address.address
  localAddress.detailAddress = address.detail_address
  localAddress.addressName = address.name
  localAddress.latitude = address.latitude
  localAddress.longitude = address.longitude
  
  // 更新使用次数
  await updateAddressUsage(address.id)
  
  emitAddressData()
}

// 更新地址使用次数
const updateAddressUsage = async (addressId: number) => {
  try {
    // TODO: 调用API更新地址使用次数
    // await updateAddressUsageCount(addressId)
    
    // 更新本地数据
    const addr = savedAddresses.value.find(a => a.id === addressId)
    if (addr) {
      addr.usage_count++
    }
  } catch (error) {
    console.error('更新地址使用次数失败:', error)
  }
}

// 地址输入变化
const onAddressChange = () => {
  selectedAddressId.value = null
  emitAddressData()
}

// 获取当前位置
const getCurrentLocation = () => {
  uni.showLoading({ title: '获取位置中...' })
  
  uni.getLocation({
    type: 'gcj02',
    success: (res) => {
      localAddress.latitude = res.latitude
      localAddress.longitude = res.longitude
      
      // 逆地理编码获取地址
      reverseGeocode(res.latitude, res.longitude)
    },
    fail: (error) => {
      console.error('获取位置失败:', error)
      showToast('获取位置失败，请检查定位权限')
    },
    complete: () => {
      uni.hideLoading()
    }
  })
}

// 逆地理编码
const reverseGeocode = async (latitude: number, longitude: number) => {
  try {
    // TODO: 调用地图API进行逆地理编码
    // const address = await reverseGeocodeApi(latitude, longitude)
    // localAddress.address = address
    
    // 模拟逆地理编码结果
    localAddress.address = '浙江省杭州市西湖区文三路'
    emitAddressData()
  } catch (error) {
    console.error('逆地理编码失败:', error)
    showToast('获取地址信息失败')
  }
}

// 打开地图选择器
const openMapSelector = () => {
  uni.chooseLocation({
    success: (res) => {
      localAddress.latitude = res.latitude
      localAddress.longitude = res.longitude
      localAddress.address = res.address
      localAddress.addressName = res.name
      
      emitAddressData()
    },
    fail: (error) => {
      console.error('地图选择失败:', error)
    }
  })
}

// 切换保存地址选项
const toggleSaveAddress = () => {
  shouldSaveAddress.value = !shouldSaveAddress.value
  if (shouldSaveAddress.value && !saveAddressName.value) {
    saveAddressName.value = localAddress.addressName
  }
}

// 发送地址数据到父组件
const emitAddressData = () => {
  emit('update:address', {
    address: localAddress.address,
    detail_address: localAddress.detailAddress,
    address_name: localAddress.addressName,
    latitude: localAddress.latitude,
    longitude: localAddress.longitude
  })
  
  // 如果选择保存地址，则保存
  if (shouldSaveAddress.value && saveAddressName.value) {
    saveNewAddress()
  }
}

// 保存新地址
const saveNewAddress = async () => {
  if (!localAddress.address || !saveAddressName.value) return
  
  try {
    // TODO: 调用API保存地址
    // await createUserAddress({
    //   name: saveAddressName.value,
    //   address: localAddress.address,
    //   detail_address: localAddress.detailAddress,
    //   latitude: localAddress.latitude,
    //   longitude: localAddress.longitude
    // })
    
    showToast('地址保存成功')
    shouldSaveAddress.value = false
    saveAddressName.value = ''
    
    // 重新加载地址列表
    await loadSavedAddresses()
  } catch (error) {
    console.error('保存地址失败:', error)
    showToast('保存地址失败')
  }
}
</script>

<style lang="scss" scoped>
.address-selector {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-base);
  margin-bottom: var(--spacing-3);
}

.saved-addresses {
  padding: var(--spacing-4);
  border-bottom: 1rpx solid var(--border-color);
}

.address-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.address-item {
  padding: var(--spacing-3);
  background-color: var(--bg-search);
  border-radius: var(--radius);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  
  &.active {
    border-color: var(--primary);
    background-color: var(--bg-primary-light);
  }
  
  &:active {
    transform: scale(0.98);
  }
}

.address-info {
  margin-bottom: var(--spacing-1);
  
  .address-name {
    display: block;
    font-size: 28rpx;
    font-weight: 500;
    color: var(--text-base);
    margin-bottom: 4rpx;
  }
  
  .address-detail {
    display: block;
    font-size: 24rpx;
    color: var(--text-secondary);
    line-height: 1.4;
  }
}

.address-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  
  .default-tag {
    padding: 2rpx 8rpx;
    background-color: var(--primary);
    color: var(--text-inverse);
    font-size: 20rpx;
    border-radius: 6rpx;
  }
  
  .usage-count {
    font-size: 20rpx;
    color: var(--text-info);
  }
}

.input-section {
  padding: var(--spacing-4);
  border-bottom: 1rpx solid var(--border-color);
}

.input-row {
  margin-bottom: var(--spacing-4);
  
  &:last-child {
    margin-bottom: 0;
  }
}

.input-label {
  display: block;
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-2);
}

.address-input {
  width: 100%;
  height: 80rpx;
  padding: 0 16rpx;
  background-color: var(--bg-input);
  border: 2rpx solid transparent;
  border-radius: var(--radius);
  font-size: 28rpx;
  color: var(--text-base);
  transition: all 0.3s ease;
  
  &:focus {
    border-color: var(--primary);
    background-color: var(--bg-card);
  }
  
  &::placeholder {
    color: var(--text-info);
  }
}

.address-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx;
  background-color: var(--bg-input);
  border: 2rpx solid transparent;
  border-radius: var(--radius);
  font-size: 28rpx;
  color: var(--text-base);
  line-height: 1.5;
  transition: all 0.3s ease;
  
  &:focus {
    border-color: var(--primary);
    background-color: var(--bg-card);
  }
  
  &::placeholder {
    color: var(--text-info);
  }
}

.location-actions {
  display: flex;
  gap: var(--spacing-3);
  margin-top: var(--spacing-3);
}

.action-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  background-color: var(--bg-search);
  border-radius: var(--radius);
  transition: all 0.3s ease;
  
  &:active {
    background-color: var(--primary);
    color: var(--text-inverse);
  }
  
  .action-icon {
    font-size: 32rpx;
  }
  
  .action-text {
    font-size: 26rpx;
    color: var(--text-secondary);
  }
}

.selected-location {
  margin-top: var(--spacing-3);
  padding: var(--spacing-3);
  background-color: var(--bg-success-light);
  border-radius: var(--radius);
  border-left: 4rpx solid var(--text-green);
}

.location-text {
  font-size: 26rpx;
  color: var(--text-green);
}

.save-option {
  padding: var(--spacing-4);
}

.save-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-3);
}

.save-label {
  font-size: 28rpx;
  color: var(--text-base);
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid var(--border-color);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  &.checked {
    background-color: var(--primary);
    border-color: var(--primary);
  }
  
  .check-icon {
    font-size: 24rpx;
    color: var(--text-inverse);
    font-weight: bold;
  }
}

.save-name-input {
  margin-top: var(--spacing-2);
}
</style>