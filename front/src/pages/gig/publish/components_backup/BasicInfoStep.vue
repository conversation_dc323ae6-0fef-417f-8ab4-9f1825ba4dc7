<template>
  <view class="basic-info-step">
    <!-- 工作标题 -->
    <view class="form-section">
      <view class="section-header">
        <text class="section-title">工作标题</text>
        <text class="required">*</text>
      </view>
      <view class="input-wrapper">
        <input
          v-model="localFormData.title"
          placeholder="请输入工作标题，如：产品包装员"
          class="form-input"
          maxlength="50"
          @input="updateFormData"
        />
        <text class="char-count">{{ localFormData.title.length }}/50</text>
      </view>
    </view>

    <!-- 工作描述 -->
    <view class="form-section">
      <view class="section-header">
        <text class="section-title">工作描述</text>
      </view>
      <textarea
        v-model="localFormData.description"
        placeholder="请详细描述工作内容、要求等"
        class="form-textarea"
        maxlength="500"
        @input="updateFormData"
      />
      <text class="char-count">{{ localFormData.description.length }}/500</text>
    </view>

    <!-- 工作时间 -->
    <view class="form-section">
      <view class="section-header">
        <text class="section-title">工作时间</text>
        <text class="required">*</text>
      </view>
      <DateTimeSelector
        :start-time="localFormData.start_time"
        :end-time="localFormData.end_time"
        @update:start-time="updateStartTime"
        @update:end-time="updateEndTime"
      />
    </view>

    <!-- 工作地点 -->
    <view class="form-section">
      <view class="section-header">
        <text class="section-title">工作地点</text>
        <text class="required">*</text>
      </view>
      <AddressSelector
        :address="localFormData.address"
        :detail-address="localFormData.detail_address"
        :address-name="localFormData.address_name"
        :latitude="localFormData.latitude"
        :longitude="localFormData.longitude"
        @update:address="updateAddress"
      />
    </view>

    <!-- 招聘人数 -->
    <view class="form-section">
      <view class="section-header">
        <text class="section-title">招聘人数</text>
        <text class="required">*</text>
      </view>
      <view class="people-count-selector">
        <view class="count-control">
          <view
            class="count-btn"
            :class="{ disabled: localFormData.people_count <= 1 }"
            @click="decreasePeopleCount"
          >
            <text class="count-icon">-</text>
          </view>
          <text class="count-number">{{ localFormData.people_count }}</text>
          <view
            class="count-btn"
            :class="{ disabled: localFormData.people_count >= 99 }"
            @click="increasePeopleCount"
          >
            <text class="count-icon">+</text>
          </view>
        </view>
        <text class="count-unit">人</text>
      </view>
    </view>

    <!-- 薪酬设置 -->
    <view class="form-section">
      <view class="section-header">
        <text class="section-title">薪酬设置</text>
        <text class="required">*</text>
      </view>
      <SalarySelector
        :salary="localFormData.salary"
        :salary-unit="localFormData.salary_unit"
        :settlement="localFormData.settlement"
        @update:salary="updateSalary"
        @update:salary-unit="updateSalaryUnit"
        @update:settlement="updateSettlement"
      />
    </view>

    <!-- 媒体上传 -->
    <view class="form-section">
      <view class="section-header">
        <text class="section-title">工作现场</text>
        <text class="section-subtitle"
          >上传照片或视频帮助求职者了解工作内容</text
        >
      </view>
      <MediaUploader
        :images="localFormData.images || []"
        :videos="localFormData.videos || []"
        @update:images="updateImages"
        @update:videos="updateVideos"
      />
    </view>

    <!-- 公司信息 -->
    <view class="form-section">
      <view class="section-header">
        <text class="section-title">公司信息</text>
      </view>
      <view class="input-wrapper">
        <input
          v-model="localFormData.company_name"
          placeholder="公司名称（选填）"
          class="form-input"
          maxlength="100"
          @input="updateFormData"
        />
      </view>
    </view>

    <!-- 紧急程度 -->
    <view class="form-section">
      <view class="section-header">
        <text class="section-title">紧急程度</text>
      </view>
      <view class="urgent-toggle">
        <view
          class="toggle-item"
          :class="{ active: !localFormData.is_urgent }"
          @click="setUrgent(false)"
        >
          <text>普通</text>
        </view>
        <view
          class="toggle-item"
          :class="{ active: localFormData.is_urgent }"
          @click="setUrgent(true)"
        >
          <text>紧急</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import type { CreateGigRequest } from "@/types/gig";
import DateTimeSelector from "./components/DateTimeSelector.vue";
import AddressSelector from "./components/AddressSelector.vue";
import SalarySelector from "./components/SalarySelector.vue";
import MediaUploader from "./components/MediaUploader.vue";

interface Props {
  formData: CreateGigRequest;
}

interface Emits {
  (e: "update:formData", data: Partial<CreateGigRequest>): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 本地表单数据
const localFormData = reactive({ ...props.formData });

// 监听父组件传入的数据变化
watch(
  () => props.formData,
  (newData) => {
    Object.assign(localFormData, newData);
  },
  { deep: true }
);

// 更新表单数据到父组件
const updateFormData = () => {
  emit("update:formData", { ...localFormData });
};

// 更新开始时间
const updateStartTime = (startTime: string) => {
  localFormData.start_time = startTime;
  updateFormData();
};

// 更新结束时间
const updateEndTime = (endTime: string) => {
  localFormData.end_time = endTime;
  updateFormData();
};

// 更新地址信息
const updateAddress = (addressData: {
  address: string;
  detail_address?: string;
  address_name?: string;
  latitude: number;
  longitude: number;
}) => {
  localFormData.address = addressData.address;
  localFormData.detail_address = addressData.detail_address || "";
  localFormData.address_name = addressData.address_name || "";
  localFormData.latitude = addressData.latitude;
  localFormData.longitude = addressData.longitude;
  updateFormData();
};

// 增加人数
const increasePeopleCount = () => {
  if (localFormData.people_count < 99) {
    localFormData.people_count++;
    updateFormData();
  }
};

// 减少人数
const decreasePeopleCount = () => {
  if (localFormData.people_count > 1) {
    localFormData.people_count--;
    updateFormData();
  }
};

// 更新薪酬
const updateSalary = (salary: number) => {
  localFormData.salary = salary;
  updateFormData();
};

// 更新薪酬单位
const updateSalaryUnit = (unit: number) => {
  localFormData.salary_unit = unit;
  updateFormData();
};

// 更新结算方式
const updateSettlement = (settlement: number) => {
  localFormData.settlement = settlement;
  updateFormData();
};

// 更新图片
const updateImages = (images: string[]) => {
  localFormData.images = images;
  // 如果选择了图片，清空视频
  if (images.length > 0) {
    localFormData.videos = [];
  }
  updateFormData();
};

// 更新视频
const updateVideos = (videos: string[]) => {
  localFormData.videos = videos;
  // 如果选择了视频，清空图片
  if (videos.length > 0) {
    localFormData.images = [];
  }
  updateFormData();
};

// 设置紧急程度
const setUrgent = (urgent: boolean) => {
  localFormData.is_urgent = urgent;
  updateFormData();
};
</script>

<style lang="scss" scoped>
.basic-info-step {
  padding: var(--spacing-4);
}

.form-section {
  margin-bottom: var(--spacing-6);

  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  display: flex;
  align-items: baseline;
  margin-bottom: var(--spacing-3);

  .section-title {
    font-size: 32rpx;
    font-weight: 500;
    color: var(--text-base);
  }

  .section-subtitle {
    font-size: 24rpx;
    color: var(--text-info);
    margin-left: var(--spacing-2);
  }

  .required {
    color: var(--text-red);
    margin-left: 4rpx;
  }
}

.input-wrapper {
  position: relative;

  .char-count {
    position: absolute;
    bottom: 12rpx;
    right: 16rpx;
    font-size: 24rpx;
    color: var(--text-info);
  }
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 16rpx;
  background-color: var(--bg-input);
  border: 2rpx solid transparent;
  border-radius: var(--radius);
  font-size: 28rpx;
  color: var(--text-base);
  transition: all 0.3s ease;

  &:focus {
    border-color: var(--primary);
    background-color: var(--bg-card);
  }

  &::placeholder {
    color: var(--text-info);
  }
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  padding: 16rpx;
  background-color: var(--bg-input);
  border: 2rpx solid transparent;
  border-radius: var(--radius);
  font-size: 28rpx;
  color: var(--text-base);
  line-height: 1.5;
  transition: all 0.3s ease;

  &:focus {
    border-color: var(--primary);
    background-color: var(--bg-card);
  }

  &::placeholder {
    color: var(--text-info);
  }
}

.char-count {
  display: block;
  text-align: right;
  font-size: 24rpx;
  color: var(--text-info);
  margin-top: var(--spacing-1);
}

.people-count-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.count-control {
  display: flex;
  align-items: center;
  background-color: var(--bg-input);
  border-radius: var(--radius);
  overflow: hidden;
}

.count-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-search);
  transition: all 0.3s ease;

  &:active:not(.disabled) {
    background-color: var(--primary);

    .count-icon {
      color: var(--text-inverse);
    }
  }

  &.disabled {
    opacity: 0.5;

    .count-icon {
      color: var(--text-disable);
    }
  }

  .count-icon {
    font-size: 32rpx;
    font-weight: 500;
    color: var(--text-base);
  }
}

.count-number {
  min-width: 100rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-base);
  background-color: var(--bg-card);
}

.count-unit {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.urgent-toggle {
  display: flex;
  background-color: var(--bg-search);
  border-radius: var(--radius);
  overflow: hidden;
}

.toggle-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: var(--text-secondary);
  transition: all 0.3s ease;

  &.active {
    background-color: var(--primary);
    color: var(--text-inverse);
    font-weight: 500;
  }
}
</style>
