<template>
  <view class="card description-card">
    <view class="description-section">
      <view class="description-label">
        <text>工作描述</text>
        <text class="char-count"
          >{{ localFormData.description.length }}/500</text
        >
      </view>
      <textarea
        v-model="localFormData.description"
        placeholder="请详细描述工作内容、要求等信息"
        class="description-textarea"
        maxlength="500"
        placeholder-class="textarea-placeholder"
      />
    </view>
  </view>
</template>

<script setup lang="ts">
import { toRef } from 'vue';
import type { CreateGigRequest } from "@/types/gig";

const props = defineProps<{ formData: CreateGigRequest }>();
const localFormData = toRef(props, 'formData');
</script>

<style lang="scss" scoped>
.card {
  margin-bottom: 24rpx;
}
.description-card {
  padding: var(--spacing);
}
.description-section {
  .description-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-3);
    text {
      font-weight: 500;
    }
    .char-count {
      font-size: 26rpx;
      color: var(--text-info);
    }
  }
}
.description-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: var(--spacing-3);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-2);
  background-color: var(--bg-input);
  line-height: var(--line-height-large);
  box-sizing: border-box;
}
.textarea-placeholder {
  color: var(--text-grey);
}
</style>
