<template>
  <view class="step-navigator">
    <view class="step-container">
      <!-- 第1步 -->
      <view
        class="step-item"
        :class="{ active: currentStep >= 1, completed: currentStep > 1 }"
      >
        <view class="step-dot">
          <text v-if="currentStep > 1" class="check-icon">✓</text>
          <text v-else>1</text>
        </view>
        <text class="step-label">基础信息</text>
      </view>

      <!-- 连接线 -->
      <view class="step-line" :class="{ active: currentStep > 1 }"></view>

      <!-- 第2步 -->
      <view
        class="step-item"
        :class="{ active: currentStep >= 2, completed: currentStep > 2 }"
      >
        <view class="step-dot">
          <text v-if="currentStep > 2" class="check-icon">✓</text>
          <text v-else>2</text>
        </view>
        <text class="step-label">招工需求</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  currentStep: number;
}

defineProps<Props>();
</script>

<style lang="scss" scoped>
.step-navigator {
  background-color: var(--bg-card);
  padding: 0 var(--spacing-2);
}

.step-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;

  .step-dot {
    width: 48rpx;
    height: 48rpx;
    border-radius: 50%;
    background-color: var(--bg-tag);
    color: var(--text-grey);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    font-weight: 500;
    margin-bottom: 8rpx;
    transition: all 0.3s ease;
  }

  .step-label {
    font-size: 24rpx;
    color: var(--text-grey);
    transition: all 0.3s ease;
  }

  &.active {
    .step-dot {
      background-color: var(--primary);
      color: var(--text-inverse);
    }

    .step-label {
      color: var(--primary);
      font-weight: 500;
    }
  }

  &.completed {
    .step-dot {
      background-color: var(--text-green);
      color: var(--text-inverse);
    }

    .check-icon {
      font-size: 32rpx;
    }
  }
}

.step-line {
  width: 80rpx;
  height: 3rpx;
  background-color: var(--border-color);
  margin: 0 var(--spacing-2);
  margin-bottom: 32rpx;
  transition: all 0.3s ease;

  &.active {
    background-color: var(--text-green);
  }
}
</style>
