<template>
  <view class="horizontal-date-selector">
    <scroll-view 
      scroll-x 
      class="date-scroll" 
      :show-scrollbar="false"
      :scroll-with-animation="true"
    >
      <view class="date-list flex space-x-3 px-4">
        <view
          v-for="dateOption in dateOptions"
          :key="dateOption.date"
          class="date-option flex-shrink-0"
          :class="{ 
            'active': selectedDate === dateOption.date,
            'today': dateOption.isToday,
            'selected': selectedDate === dateOption.date
          }"
          @click="selectDate(dateOption.date)"
        >
          <view class="date-content text-center">
            <text class="date-label text-sm font-medium">{{ dateOption.label }}</text>
            <text class="date-value text-base mt-1">{{ dateOption.dateText }}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { formatDate, DateFormat } from '@/utils/core/date'

interface Props {
  selectedDate: string
}

interface Emits {
  (e: 'update:date', date: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 生成接下来7天的日期选项
const dateOptions = computed(() => {
  const options = []
  const today = new Date()
  
  for (let i = 0; i < 7; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() + i)
    
    const dateStr = formatDate(date, DateFormat.DATE_ONLY)
    let label = ''
    
    if (i === 0) {
      label = '今' + getWeekDay(date)
    } else if (i === 1) {
      label = '明' + getWeekDay(date)
    } else if (i === 2) {
      label = '后' + getWeekDay(date)
    } else {
      label = getWeekDay(date)
    }
    
    options.push({
      date: dateStr,
      label,
      dateText: formatDate(date, DateFormat.MONTH_DAY),
      isToday: i === 0
    })
  }
  
  return options
})

// 获取星期几的中文表示
const getWeekDay = (date: Date): string => {
  const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  return weekDays[date.getDay()]
}

// 选择日期
const selectDate = (date: string) => {
  emit('update:date', date)
}
</script>

<style lang="scss" scoped>
.horizontal-date-selector {
  width: 100%;
  padding: var(--spacing-3) 0;
  background: var(--bg-card);
  border-radius: var(--radius-3);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.date-scroll {
  width: 100%;
  white-space: nowrap;
}

.date-list {
  display: inline-flex;
  min-width: 100%;
  padding: 0 var(--spacing-4);
  gap: var(--spacing-3);
}

.date-option {
  min-width: 140rpx;
  padding: var(--spacing-4) var(--spacing-3);
  background: var(--bg-search);
  border-radius: var(--radius-3);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2rpx solid transparent;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:active {
    transform: scale(0.96);

    &::before {
      opacity: 1;
    }
  }

  &.today {
    background: linear-gradient(135deg, var(--bg-primary-light), rgba(255, 109, 0, 0.1));
    border-color: var(--primary);
    box-shadow: 0 4rpx 12rpx rgba(255, 109, 0, 0.15);

    .date-label {
      color: var(--primary);
      font-weight: 600;
    }

    .date-value {
      color: var(--primary);
      font-weight: 600;
    }
  }

  &.active {
    background: linear-gradient(135deg, var(--primary), #ff8c00);
    border-color: var(--primary);
    box-shadow: 0 6rpx 20rpx rgba(255, 109, 0, 0.3);
    transform: translateY(-2rpx);

    .date-label, .date-value {
      color: var(--text-inverse);
      font-weight: 600;
    }
  }
}

.date-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6rpx;
  position: relative;
  z-index: 1;
}

.date-label {
  font-size: 22rpx;
  color: var(--text-secondary);
  font-weight: 500;
  line-height: 1.2;
  letter-spacing: 0.5rpx;
  text-transform: uppercase;
}

.date-value {
  font-size: 32rpx;
  color: var(--text-base);
  font-weight: 600;
  line-height: 1.1;
  letter-spacing: -0.5rpx;
}

/* 针对微信小程序的滚动条隐藏 */
::-webkit-scrollbar {
  display: none;
}
</style>