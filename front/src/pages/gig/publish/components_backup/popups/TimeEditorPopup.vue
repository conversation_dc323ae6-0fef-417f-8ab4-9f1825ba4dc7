<template>
  <tui-bottom-popup :show="show" @close="$emit('close')" :height="700">
    <view class="popup-container">
      <view class="popup-header">
        <text class="popup-title">选择每日工作时间</text>
        <view class="popup-close" @click="$emit('close')">
          <text class="close-icon">×</text>
        </view>
      </view>

      <view class="popup-content">
        <!-- 时间显示区域 -->
        <view class="time-display-section">
          <view class="time-display-row">
            <view class="time-display-item">
              <text class="time-label">开工时间</text>
              <text class="time-value">{{ displayStartTime }}</text>
            </view>
            <view class="time-separator">
              <text class="separator-line">——</text>
              <text class="duration-text">工作时长</text>
              <text class="separator-line">——</text>
            </view>
            <view class="time-display-item">
              <text class="time-label">收工时间</text>
              <text class="time-value">{{ displayEndTime }}</text>
              <text v-if="isNextDay" class="next-day-label">次日</text>
            </view>
          </view>
          
          <view class="duration-display">
            <text class="duration-value">共{{ workHours }}小时</text>
            <text class="duration-subtitle">每日工作时长</text>
          </view>
        </view>

        <!-- 时间选择器 -->
        <view class="time-picker-section">
          <view class="picker-container">
            <!-- 小时选择 -->
            <view class="picker-column">
              <picker-view
                :value="[selectedStartHour]"
                @change="onStartHourChange"
                class="picker-view"
              >
                <picker-view-column>
                  <view 
                    v-for="hour in hours" 
                    :key="hour"
                    class="picker-item"
                  >
                    {{ String(hour).padStart(2, '0') }}
                  </view>
                </picker-view-column>
              </picker-view>
              <text class="picker-label">时</text>
            </view>
            
            <!-- 分钟选择 -->
            <view class="picker-column">
              <picker-view
                :value="[selectedStartMinute]"
                @change="onStartMinuteChange"
                class="picker-view"
              >
                <picker-view-column>
                  <view 
                    v-for="minute in minutes" 
                    :key="minute"
                    class="picker-item"
                  >
                    {{ String(minute).padStart(2, '0') }}
                  </view>
                </picker-view-column>
              </picker-view>
              <text class="picker-label">分</text>
            </view>
            
            <!-- 结束时间小时选择 -->
            <view class="picker-column">
              <picker-view
                :value="[selectedEndHour]"
                @change="onEndHourChange"
                class="picker-view"
              >
                <picker-view-column>
                  <view 
                    v-for="hour in hours" 
                    :key="hour"
                    class="picker-item"
                  >
                    {{ String(hour).padStart(2, '0') }}
                  </view>
                </picker-view-column>
              </picker-view>
              <text class="picker-label">时</text>
            </view>
            
            <!-- 结束时间分钟选择 -->
            <view class="picker-column">
              <picker-view
                :value="[selectedEndMinute]"
                @change="onEndMinuteChange"
                class="picker-view"
              >
                <picker-view-column>
                  <view 
                    v-for="minute in minutes" 
                    :key="minute"
                    class="picker-item"
                  >
                    {{ String(minute).padStart(2, '0') }}
                  </view>
                </picker-view-column>
              </picker-view>
              <text class="picker-label">分</text>
            </view>
          </view>
          
          <view class="picker-indicators">
            <text class="indicator-label">开始</text>
            <text class="indicator-label">结束</text>
          </view>
        </view>
      </view>

      <view class="popup-footer">
        <tui-button
          type="primary"
          width="100%"
          height="88rpx"
          @click="confirmSelection"
        >
          确定
        </tui-button>
      </view>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import TuiBottomPopup from '@/components/thorui/tui-bottom-popup/tui-bottom-popup.vue'

interface Props {
  show?: boolean
  startTime?: string
  endTime?: string
}

interface Emits {
  (e: 'close'): void
  (e: 'confirm', data: { startTime: string, endTime: string }): void
}

const props = withDefaults(defineProps<Props>(), {
  show: true,
  startTime: '',
  endTime: ''
})

const emit = defineEmits<Emits>()

// 时间选择器数据
const hours = Array.from({ length: 24 }, (_, i) => i)
const minutes = Array.from({ length: 12 }, (_, i) => i * 5) // 0, 5, 10, ..., 55

// 选中的时间索引
const selectedStartHour = ref(18)
const selectedStartMinute = ref(10) // 对应50分钟
const selectedEndHour = ref(2)
const selectedEndMinute = ref(10) // 对应50分钟

// 显示时间
const displayStartTime = computed(() => {
  const hour = String(selectedStartHour.value).padStart(2, '0')
  const minute = String(minutes[selectedStartMinute.value]).padStart(2, '0')
  return `${hour}:${minute}`
})

const displayEndTime = computed(() => {
  const hour = String(selectedEndHour.value).padStart(2, '0')
  const minute = String(minutes[selectedEndMinute.value]).padStart(2, '0')
  return `${hour}:${minute}`
})

// 初始化时间
onMounted(() => {
  if (props.startTime) {
    const time = props.startTime.split(' ')[1] || '18:50:00'
    const [hour, minute] = time.split(':').map(Number)
    selectedStartHour.value = hour
    selectedStartMinute.value = minutes.findIndex(m => m >= minute) || 10
  }
  if (props.endTime) {
    const time = props.endTime.split(' ')[1] || '02:50:00'
    const [hour, minute] = time.split(':').map(Number)
    selectedEndHour.value = hour
    selectedEndMinute.value = minutes.findIndex(m => m >= minute) || 10
  }
})

// 计算工作小时数
const workHours = computed(() => {
  const startTotalMinutes = selectedStartHour.value * 60 + minutes[selectedStartMinute.value]
  let endTotalMinutes = selectedEndHour.value * 60 + minutes[selectedEndMinute.value]
  
  // 处理跨日情况
  if (endTotalMinutes <= startTotalMinutes) {
    endTotalMinutes += 24 * 60
  }
  
  const durationMinutes = endTotalMinutes - startTotalMinutes
  return Math.round(durationMinutes / 60)
})

// 判断是否跨日
const isNextDay = computed(() => {
  return selectedEndHour.value < selectedStartHour.value
})

// 时间选择器变化事件
const onStartHourChange = (e: any) => {
  selectedStartHour.value = e.detail.value[0]
}

const onStartMinuteChange = (e: any) => {
  selectedStartMinute.value = e.detail.value[0]
}

const onEndHourChange = (e: any) => {
  selectedEndHour.value = e.detail.value[0]
}

const onEndMinuteChange = (e: any) => {
  selectedEndMinute.value = e.detail.value[0]
}

// 确认选择
const confirmSelection = () => {
  // 获取当前日期
  const today = new Date()
  const dateStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`
  
  const startTimeStr = `${dateStr} ${displayStartTime.value}:00`
  const endTimeStr = `${dateStr} ${displayEndTime.value}:00`
  
  emit('confirm', {
    startTime: startTimeStr,
    endTime: endTimeStr
  })
}
</script>

<style lang="scss" scoped>
.popup-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--bg-card);
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-bottom: 2rpx solid var(--border-light);
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-base);
}

.popup-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--bg-search);
}

.close-icon {
  font-size: 36rpx;
  color: var(--text-secondary);
  font-weight: 300;
}

.popup-content {
  flex: 1;
  overflow-y: auto;
  background: var(--bg-card);
}

.time-display-section {
  padding: var(--spacing-6) var(--spacing-4);
  text-align: center;
}

.time-display-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.time-display-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  position: relative;
}

.time-label {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.time-value {
  font-size: 72rpx;
  font-weight: 600;
  line-height: 1;
}

.next-day-label {
  position: absolute;
  top: -8rpx;
  right: -16rpx;
  background: var(--bg-warning-light);
  color: var(--text-yellow);
  padding: 4rpx 8rpx;
  border-radius: var(--radius-1);
  font-size: 20rpx;
  font-weight: 500;
}

.time-separator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-1);
  margin: 0 var(--spacing-2);
}

.separator-line {
  color: var(--text-secondary);
  font-size: 24rpx;
}

.duration-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  white-space: nowrap;
}

.duration-display {
  text-align: center;
  padding-top: var(--spacing-4);
  border-top: 2rpx solid var(--border-light);
}

.duration-value {
  font-size: 48rpx;
  font-weight: 600;
  color: var(--primary);
  display: block;
  margin-bottom: var(--spacing-1);
}

.duration-subtitle {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.time-picker-section {
  padding: var(--spacing-4);
  background: var(--bg-page);
}

.picker-container {
  display: flex;
  justify-content: space-between;
  background: var(--bg-card);
  border-radius: var(--radius-3);
  padding: var(--spacing-3);
  margin-bottom: var(--spacing-3);
}

.picker-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.picker-view {
  width: 100%;
  height: 240rpx;
}

.picker-item {
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: var(--text-base);
  font-weight: 500;
}

.picker-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-top: var(--spacing-2);
  text-align: center;
}

.picker-indicators {
  display: flex;
  justify-content: space-around;
  padding: 0 var(--spacing-3);
}

.indicator-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  text-align: center;
  flex: 1;
}

.popup-footer {
  padding: var(--spacing-4);
  border-top: 2rpx solid var(--border-light);
  background: var(--bg-card);
}
</style>