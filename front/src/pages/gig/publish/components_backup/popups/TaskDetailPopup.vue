<template>
  <tui-bottom-popup :show="true" @close="$emit('close')" :height="800">
    <view class="popup-container">
      <view class="popup-header">
        <text class="popup-title">任务详情</text>
        <view class="popup-close" @click="$emit('close')">
          <text class="close-icon">×</text>
        </view>
      </view>

      <view class="popup-content">
        <!-- 招工标题 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">招工标题</text>
          </view>
          <view class="input-container">
            <input
              v-model="localFormData.title"
              placeholder="电子厂普工"
              class="form-input"
              :maxlength="20"
            />
            <view class="char-count">{{ localFormData.title.length }}/20</view>
          </view>
        </view>

        <!-- 详情描述 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">详情描述（非必填）</text>
          </view>
          <view class="textarea-container">
            <textarea
              v-model="localFormData.description"
              placeholder="补充工作要求或详细内容，请勿填写手机号或微信号"
              class="form-textarea"
              :maxlength="200"
            />
            <view class="voice-input-btn">
              <text class="voice-icon">🎤</text>
              <text class="voice-text">语音输入</text>
            </view>
          </view>
          
          <!-- 上传媒体 -->
          <view class="media-upload-section">
            <view class="media-upload-item">
              <view class="upload-icon video-icon">
                <text class="icon-text">📹</text>
              </view>
              <text class="upload-text">上传视频</text>
            </view>
            <view class="media-upload-item required">
              <view class="upload-icon photo-icon">
                <text class="icon-text">📷</text>
              </view>
              <text class="upload-text">上传照片</text>
              <view class="required-badge">必填</view>
            </view>
          </view>
          
          <view class="upload-tips">
            <text class="tips-text">上传有工作内容的视频或照片，增加50%的接单率</text>
            <view class="tips-actions">
              <text class="tips-link">图片示例</text>
              <text class="tips-link">视频示例</text>
            </view>
          </view>
        </view>

        <!-- 工作要求或福利 -->
        <view class="form-section">
          <view class="section-header">
            <text class="section-title">工作要求或福利（可多选）</text>
          </view>
          <view class="tags-container">
            <view class="tags-row">
              <view 
                v-for="tag in workTags.slice(0, 4)" 
                :key="tag"
                class="tag-item"
                :class="{ active: selectedTags.includes(tag) }"
                @click="toggleTag(tag)"
              >
                <text class="tag-text">{{ tag }}</text>
              </view>
            </view>
            <view class="tags-row">
              <view 
                v-for="tag in workTags.slice(4, 8)" 
                :key="tag"
                class="tag-item"
                :class="{ active: selectedTags.includes(tag) }"
                @click="toggleTag(tag)"
              >
                <text class="tag-text">{{ tag }}</text>
              </view>
            </view>
            <view class="tags-row">
              <view 
                v-for="tag in workTags.slice(8)" 
                :key="tag"
                class="tag-item"
                :class="{ active: selectedTags.includes(tag) }"
                @click="toggleTag(tag)"
              >
                <text class="tag-text">{{ tag }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="popup-footer">
        <tui-button
          type="primary"
          width="100%"
          height="88rpx"
          :disabled="!canConfirm"
          @click="confirmSelection"
        >
          确定
        </tui-button>
      </view>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
import { reactive, computed } from 'vue'

interface Props {
  title?: string
  description?: string
  peopleCount?: number
  salary?: number
  address?: string
  detailAddress?: string
}

interface Emits {
  (e: 'close'): void
  (e: 'confirm', data: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 本地表单数据
const localFormData = reactive({
  title: props.title || '电子厂普工',
  description: props.description || '',
  requirements: '',
  images: [] as string[],
  videos: [] as string[]
})

// 工作要求标签
const workTags = [
  '欢迎新手', '只要熟手', '提前到场', '禁止吸烟',
  '坐着上班', '站着上班', '吃饭打时', '吃饭不打时',
  '包工作餐', '认真负责', '手脚麻利', '不磨洋工',
  '可无经验', '中午包饭', '晚上包饭'
]

// 选中的标签
const selectedTags = ref<string[]>([])

// 切换标签选中状态
const toggleTag = (tag: string) => {
  const index = selectedTags.value.indexOf(tag)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tag)
  }
}



// 是否可以确认
const canConfirm = computed(() => {
  return !!(localFormData.title && localFormData.title.trim())
})

// 确认选择
const confirmSelection = () => {
  if (!canConfirm.value) {
    uni.showToast({
      title: '请填写招工标题',
      icon: 'none'
    })
    return
  }
  
  const result = {
    title: localFormData.title,
    description: localFormData.description,
    requirements: selectedTags.value.join(','),
    images: localFormData.images,
    videos: localFormData.videos
  }
  
  emit('confirm', result)
}
</script>

<style lang="scss" scoped>
.popup-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--bg-card);
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-bottom: 2rpx solid var(--border-light);
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-base);
}

.popup-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--bg-search);
}

.close-icon {
  font-size: 36rpx;
  color: var(--text-secondary);
  font-weight: 300;
}

.popup-content {
  flex: 1;
  overflow-y: auto;
  padding-bottom: var(--spacing-4);
}

.form-section {
  margin-bottom: var(--spacing-6);
}

.section-header {
  padding: var(--spacing-4) var(--spacing-4) var(--spacing-3);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
}

.input-container {
  padding: 0 var(--spacing-4);
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 var(--spacing-3);
  background: var(--bg-search);
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-2);
  font-size: 32rpx;
  color: var(--text-base);
  transition: all 0.3s ease;

  &:focus {
    border-color: var(--primary);
    background: var(--bg-card);
  }

  &::placeholder {
    color: var(--text-grey);
  }
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-top: var(--spacing-2);
}

.textarea-container {
  padding: 0 var(--spacing-4);
  position: relative;
}

.form-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: var(--spacing-3);
  background: var(--bg-search);
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-2);
  font-size: 28rpx;
  color: var(--text-base);
  line-height: 1.5;
  resize: none;

  &:focus {
    border-color: var(--primary);
    background: var(--bg-card);
  }

  &::placeholder {
    color: var(--text-grey);
  }
}

.voice-input-btn {
  position: absolute;
  right: var(--spacing-5);
  bottom: var(--spacing-3);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-2) var(--spacing-3);
  background: var(--bg-warning-light);
  border-radius: var(--radius-2);
  border: 1rpx solid #FFC107;
}

.voice-icon {
  font-size: 24rpx;
}

.voice-text {
  font-size: 24rpx;
  color: #F57C00;
  font-weight: 500;
}

.media-upload-section {
  display: flex;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  background: var(--bg-warning-light);
  margin: var(--spacing-4) var(--spacing-4) 0;
  border-radius: var(--radius-2);
}

.media-upload-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  position: relative;

  &.required {
    .upload-icon {
      border-color: var(--primary);
    }
  }
}

.upload-icon {
  width: 120rpx;
  height: 120rpx;
  border: 4rpx solid var(--border-light);
  border-radius: var(--radius-2);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-card);

  &.video-icon {
    background: #FFF3E0;
  }

  &.photo-icon {
    background: #E3F2FD;
  }
}

.icon-text {
  font-size: 48rpx;
}

.upload-text {
  font-size: 26rpx;
  color: var(--text-base);
  font-weight: 500;
}

.required-badge {
  position: absolute;
  top: -8rpx;
  right: 8rpx;
  background: var(--text-red);
  color: var(--text-inverse);
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: var(--radius-1);
}

.upload-tips {
  padding: var(--spacing-3) var(--spacing-4);
  background: var(--bg-card);
  margin: 0 var(--spacing-4);
  border-radius: var(--radius-2);
  border: 1rpx solid var(--border-light);
}

.tips-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.4;
  display: block;
  margin-bottom: var(--spacing-2);
}

.tips-actions {
  display: flex;
  gap: var(--spacing-4);
}

.tips-link {
  font-size: 24rpx;
  color: var(--primary);
  text-decoration: underline;
}

.tags-container {
  padding: 0 var(--spacing-4);
}

.tags-row {
  display: flex;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-3);
}

.tag-item {
  flex: 1;
  padding: var(--spacing-2) var(--spacing-3);
  background: var(--bg-search);
  border: 2rpx solid var(--border-light);
  border-radius: var(--radius-2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  min-height: 72rpx;

  &:active {
    transform: scale(0.95);
  }

  &.active {
    background: var(--bg-primary-light);
    border-color: var(--primary);

    .tag-text {
      color: var(--primary);
      font-weight: 600;
    }
  }
}

.tag-text {
  font-size: 26rpx;
  color: var(--text-base);
  text-align: center;
  line-height: 1.2;
}

.popup-footer {
  padding: var(--spacing-4);
  border-top: 2rpx solid var(--border-light);
  background: var(--bg-card);
}
</style>