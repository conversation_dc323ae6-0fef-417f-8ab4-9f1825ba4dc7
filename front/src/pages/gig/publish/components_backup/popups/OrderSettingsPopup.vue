<template>
  <tui-bottom-popup :show="true" @close="$emit('close')" :height="800">
    <view class="popup-container">
      <view class="popup-header">
        <text class="popup-title">零工接单设置</text>
        <view class="popup-close" @click="$emit('close')">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="popup-content">
        <view class="settings-form">
          <!-- 零工报名时 -->
          <view class="settings-section">
            <text class="section-title">零工报名时</text>
            
            <!-- 接听零工电话设置 -->
            <view class="setting-item">
              <view class="setting-header">
                <view class="setting-icon">📱</view>
                <view class="setting-info">
                  <text class="setting-title">接听零工电话设置</text>
                  <text class="setting-desc">是否需要零工电话沟通</text>
                </view>
              </view>
              <view class="setting-value">
                <view class="requirement-indicator required">
                  <text class="requirement-text">必须拨打电话</text>
                </view>
                <text class="setting-arrow">›</text>
              </view>
            </view>
          </view>
          
          <!-- 零工开工时 -->
          <view class="settings-section">
            <text class="section-title">零工开工时</text>
            
            <!-- 开工码 -->
            <view class="setting-item">
              <view class="setting-header">
                <view class="setting-icon">🔢</view>
                <view class="setting-info">
                  <text class="setting-title">开工码</text>
                  <text class="setting-desc">防止零工提前开工，避开打卡</text>
                </view>
              </view>
              <view class="setting-value">
                <view class="switch-container">
                  <switch 
                    :checked="localSettings.enable_work_code" 
                    @change="onWorkCodeChange"
                    color="#FF6D00"
                  />
                  <text class="switch-status" :class="{ active: localSettings.enable_work_code }">
                    {{ localSettings.enable_work_code ? '已开启' : '未开启' }}
                  </text>
                </view>
              </view>
            </view>
            
            <!-- 人脸打卡 -->
            <view class="setting-item">
              <view class="setting-header">
                <view class="setting-icon">👤</view>
                <view class="setting-info">
                  <text class="setting-title">人脸打卡</text>
                  <text class="setting-desc">打卡验证人脸，确保本人干活</text>
                </view>
              </view>
              <view class="setting-value">
                <view class="switch-container">
                  <switch 
                    :checked="localSettings.enable_face_check" 
                    @change="onFaceCheckChange"
                    color="#FF6D00"
                  />
                  <text class="switch-status" :class="{ active: localSettings.enable_face_check }">
                    {{ localSettings.enable_face_check ? '已开启' : '未开启' }}
                  </text>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 零工早退时 -->
          <view class="settings-section">
            <text class="section-title">零工早退时</text>
            
            <!-- 早退码 -->
            <view class="setting-item">
              <view class="setting-header">
                <view class="setting-icon">🏃</view>
                <view class="setting-info">
                  <text class="setting-title">早退码</text>
                  <text class="setting-desc">防止零工不打招呼就早退跑路</text>
                </view>
              </view>
              <view class="setting-value">
                <view class="switch-container">
                  <switch 
                    :checked="localSettings.enable_early_leave_code" 
                    @change="onEarlyLeaveCodeChange"
                    color="#FF6D00"
                  />
                  <text class="switch-status" :class="{ active: localSettings.enable_early_leave_code }">
                    {{ localSettings.enable_early_leave_code ? '已开启' : '未开启' }}
                  </text>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 结算报酬时 -->
          <view class="settings-section">
            <text class="section-title">结算报酬时</text>
            
            <!-- 考勤计时方式 -->
            <view class="setting-item">
              <view class="setting-header">
                <view class="setting-icon">⏰</view>
                <view class="setting-info">
                  <text class="setting-title">考勤计时方式</text>
                  <text class="setting-desc">选择报酬计算方式</text>
                </view>
              </view>
              <view class="setting-value">
                <text class="value-text">按零工打卡时间</text>
                <text class="setting-arrow">›</text>
              </view>
            </view>
          </view>
          
          <!-- 审核设置 -->
          <view class="settings-section">
            <text class="section-title">审核设置</text>
            
            <!-- 审核方式 -->
            <view class="setting-item">
              <view class="setting-header">
                <view class="setting-info">
                  <text class="setting-title">审核方式</text>
                </view>
              </view>
              <view class="setting-value">
                <picker
                  mode="selector"
                  :range="approvalModeOptions"
                  range-key="label"
                  :value="approvalModeIndex"
                  @change="onApprovalModeChange"
                >
                  <view class="picker-value">
                    <text>{{ currentApprovalMode.label }}</text>
                    <text class="setting-arrow">›</text>
                  </view>
                </picker>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="popup-footer">
        <tui-button
          type="primary"
          width="100%"
          height="88rpx"
          @click="confirmSelection"
        >
          确定
        </tui-button>
      </view>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
import { GIG_APPROVAL_MODE_OPTIONS } from '@/constants/gig'
import type { CreateGigRequest } from '@/types/gig'

interface Props {
  formData: CreateGigRequest
}

interface Emits {
  (e: 'close'): void
  (e: 'confirm', data: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 本地设置
const localSettings = reactive({
  enable_work_code: false,
  enable_face_check: true,
  enable_early_leave_code: false,
  approval_mode: props.formData.approval_mode || 'manual',
  check_in_method: props.formData.check_in_method || 'work_code'
})

// 审核方式选项
const approvalModeOptions = GIG_APPROVAL_MODE_OPTIONS

// 当前选中的审核方式索引
const approvalModeIndex = computed(() => {
  return approvalModeOptions.findIndex(option => option.value === localSettings.approval_mode)
})

// 当前审核方式
const currentApprovalMode = computed(() => {
  return approvalModeOptions.find(option => option.value === localSettings.approval_mode) || approvalModeOptions[0]
})

// 开工码变化
const onWorkCodeChange = (e: any) => {
  localSettings.enable_work_code = e.detail.value
}

// 人脸打卡变化
const onFaceCheckChange = (e: any) => {
  localSettings.enable_face_check = e.detail.value
}

// 早退码变化
const onEarlyLeaveCodeChange = (e: any) => {
  localSettings.enable_early_leave_code = e.detail.value
}

// 审核方式变化
const onApprovalModeChange = (e: any) => {
  const index = e.detail.value
  const selectedOption = approvalModeOptions[index]
  localSettings.approval_mode = selectedOption.value
}

// 确认选择
const confirmSelection = () => {
  // 根据设置更新打卡方式
  let checkInMethod = 'none'
  if (localSettings.enable_work_code && localSettings.enable_face_check) {
    checkInMethod = 'work_code_face'
  } else if (localSettings.enable_work_code) {
    checkInMethod = 'work_code'
  } else if (localSettings.enable_face_check) {
    checkInMethod = 'face'
  }
  
  emit('confirm', {
    approval_mode: localSettings.approval_mode,
    check_in_method: checkInMethod,
    ...localSettings
  })
}
</script>

<style lang="scss" scoped>
.popup-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--bg-card);
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-bottom: 2rpx solid var(--border-light);
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-base);
}

.popup-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--bg-search);
}

.close-icon {
  font-size: 36rpx;
  color: var(--text-secondary);
  font-weight: 300;
}

.popup-content {
  flex: 1;
  overflow-y: auto;
  background: var(--bg-page);
}

.settings-form {
  padding: var(--spacing-4);
}

.settings-section {
  margin-bottom: var(--spacing-6);
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  display: block;
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-3);
  padding-left: var(--spacing-2);
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-3);
  background: var(--bg-card);
  border-radius: var(--radius-3);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  
  &:last-child {
    margin-bottom: 0;
  }
}

.setting-header {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-icon {
  font-size: 36rpx;
  margin-right: var(--spacing-3);
  width: 50rpx;
  text-align: center;
}

.setting-info {
  flex: 1;
}

.setting-title {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-base);
  margin-bottom: 6rpx;
}

.setting-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

.setting-value {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.requirement-indicator {
  padding: 8rpx 16rpx;
  border-radius: var(--radius-2);
  
  &.required {
    background: var(--bg-primary-light);
    border: 2rpx solid var(--primary);
    
    .requirement-text {
      color: var(--primary);
      font-weight: 600;
      font-size: 24rpx;
    }
  }
}

.switch-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.switch-status {
  font-size: 26rpx;
  color: var(--text-secondary);
  font-weight: 500;
  
  &.active {
    color: var(--primary);
    font-weight: 600;
  }
}

.setting-arrow {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-left: var(--spacing-1);
}

.picker-value {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  
  text {
    font-size: 28rpx;
    color: var(--text-base);
    font-weight: 500;
  }
}

.popup-footer {
  padding: var(--spacing-4);
  border-top: 2rpx solid var(--border-light);
  background: var(--bg-card);
}
</style>