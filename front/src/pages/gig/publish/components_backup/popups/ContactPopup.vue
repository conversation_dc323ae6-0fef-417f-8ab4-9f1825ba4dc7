<template>
  <tui-bottom-popup :show="true" @close="$emit('close')" :height="700">
    <view class="popup-container">
      <view class="popup-header">
        <text class="popup-title">联系电话</text>
        <view class="popup-close" @click="$emit('close')">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="popup-content">
        <!-- 报名接单联系人 -->
        <view class="contact-section">
          <view class="section-header">
            <text class="section-title">报名接单联系人</text>
            <text class="section-subtitle">零工接单时按打的电话，方便沟通面试</text>
          </view>
          
          <view class="contact-item" :class="{ active: applicationContact.active }">
            <view class="contact-icon">
              <text class="icon-phone">📞</text>
            </view>
            <view class="contact-info">
              <text class="contact-name">我 {{ applicationContact.phone }}</text>
            </view>
            <view class="contact-status verified">
              <text class="status-text">优先联系</text>
            </view>
            <view class="contact-actions">
              <text class="action-text">移除</text>
              <text class="action-text">换人</text>
            </view>
          </view>
          
          <view class="add-contact-btn" @click="showAddApplicationContact">
            <text class="add-icon">+</text>
            <text class="add-text">增加备用联系人</text>
          </view>
        </view>

        <!-- 现场开工联系人 -->
        <view class="contact-section">
          <view class="section-header">
            <text class="section-title">现场开工联系人</text>
            <text class="section-subtitle">零工到现场后问路，工作安排</text>
          </view>
          
          <view class="contact-item" :class="{ active: workContact.active }">
            <view class="contact-icon">
              <text class="icon-phone">📞</text>
            </view>
            <view class="contact-info">
              <text class="contact-name">我 {{ workContact.phone }}</text>
            </view>
            <view class="contact-status verified">
              <text class="status-text">优先联系</text>
            </view>
            <view class="contact-actions">
              <text class="action-text">移除</text>
              <text class="action-text">换人</text>
            </view>
          </view>
          
          <view class="add-contact-btn" @click="showAddWorkContact">
            <text class="add-icon">+</text>
            <text class="add-text">增加备用联系人</text>
          </view>
        </view>
      </view>
      
      <view class="popup-footer">
        <tui-button
          type="primary"
          width="100%"
          height="88rpx"
          @click="confirmSelection"
        >
          确定
        </tui-button>
      </view>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
interface Contact {
  name: string
  phone: string
  tag: string
}

interface Props {
  contactName?: string
  contactPhone?: string
}

interface Emits {
  (e: 'close'): void
  (e: 'confirm', data: { contactName: string, contactPhone: string }): void
}

const props = withDefaults(defineProps<Props>(), {
  contactName: '',
  contactPhone: ''
})

const emit = defineEmits<Emits>()

// 报名接单联系人
const applicationContact = reactive({
  name: '我',
  phone: '13126606919',
  active: true
})

// 现场开工联系人
const workContact = reactive({
  name: '我',
  phone: '13126606919',
  active: true
})

// 初始化联系人信息
onMounted(() => {
  if (props.contactPhone) {
    applicationContact.phone = props.contactPhone
    workContact.phone = props.contactPhone
  }
  if (props.contactName) {
    applicationContact.name = props.contactName
    workContact.name = props.contactName
  }
})

// 显示添加报名联系人
const showAddApplicationContact = () => {
  // TODO: 实现添加报名联系人逻辑
  uni.showToast({
    title: '此功能暂未开放',
    icon: 'none'
  })
}

// 显示添加工作联系人
const showAddWorkContact = () => {
  // TODO: 实现添加工作联系人逻辑
  uni.showToast({
    title: '此功能暂未开放',
    icon: 'none'
  })
}

// 确认选择
const confirmSelection = () => {
  emit('confirm', {
    contactName: applicationContact.name,
    contactPhone: applicationContact.phone
  })
}
</script>

<style lang="scss" scoped>
.popup-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--bg-card);
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-bottom: 2rpx solid var(--border-light);
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-base);
}

.popup-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--bg-search);
}

.close-icon {
  font-size: 36rpx;
  color: var(--text-secondary);
  font-weight: 300;
}

.popup-content {
  flex: 1;
  overflow-y: auto;
  background: var(--bg-page);
}

.contact-section {
  margin-bottom: var(--spacing-6);
}

.section-header {
  padding: var(--spacing-4) var(--spacing-4) var(--spacing-3);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
  display: block;
  margin-bottom: var(--spacing-1);
}

.section-subtitle {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-4);
  margin: 0 var(--spacing-4) var(--spacing-3);
  background: var(--bg-card);
  border-radius: var(--radius-3);
  border: 3rpx solid #FFD700;
  position: relative;
  
  &.active {
    border-color: var(--primary);
    background: var(--bg-primary-light);
  }
}

.contact-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: var(--radius-2);
  background: var(--bg-search);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-3);
}

.icon-phone {
  font-size: 32rpx;
}

.contact-info {
  flex: 1;
}

.contact-name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
  line-height: 1.2;
}

.contact-status {
  margin-right: var(--spacing-3);
  
  &.verified {
    .status-text {
      background: rgba(52, 199, 89, 0.1);
      color: #34c759;
      padding: 6rpx 12rpx;
      border-radius: var(--radius-1);
      font-size: 22rpx;
      font-weight: 500;
    }
  }
}

.contact-actions {
  display: flex;
  gap: var(--spacing-3);
}

.action-text {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.add-contact-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
  margin: 0 var(--spacing-4);
  background: var(--bg-card);
  border-radius: var(--radius-3);
  border: 2rpx dashed var(--border-light);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    background: var(--bg-search);
  }
}

.add-icon {
  font-size: 32rpx;
  color: var(--primary);
  margin-right: var(--spacing-2);
  font-weight: 300;
}

.add-text {
  font-size: 28rpx;
  color: var(--primary);
  font-weight: 500;
}

.popup-footer {
  padding: var(--spacing-4);
  border-top: 2rpx solid var(--border-light);
  background: var(--bg-card);
}
</style>