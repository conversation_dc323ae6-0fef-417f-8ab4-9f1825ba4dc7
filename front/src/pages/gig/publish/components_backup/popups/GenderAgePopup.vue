<template>
  <tui-bottom-popup :show="show" @close="$emit('close')" :height="600">
    <view class="popup-container">
      <view class="popup-header">
        <text class="popup-title">性别年龄</text>
        <view class="popup-close" @click="$emit('close')">
          <text class="close-icon">×</text>
        </view>
      </view>

      <view class="popup-content">
        <!-- 性别选择 -->
        <view class="gender-section">
          <view class="gender-options">
            <view
              v-for="option in genderOptions"
              :key="option.value"
              class="gender-option"
              :class="{ active: selectedGender === option.value }"
              @click="selectedGender = option.value"
            >
              <text class="option-text">{{ option.label }}</text>
            </view>
          </view>
        </view>

        <!-- 年龄范围显示 -->
        <view class="age-display-section">
          <text class="age-display-text">{{ selectedAgeMin }}岁~{{ selectedAgeMax === 60 ? '不限' : selectedAgeMax + '岁' }}</text>
        </view>

        <!-- 年龄滑块 -->
        <view class="age-slider-section">
          <view class="age-range-slider">
            <!-- 滑块背景轨道 -->
            <view class="slider-track">
              <view 
                class="slider-range" 
                :style="{ 
                  left: getSliderLeft() + '%', 
                  width: getSliderWidth() + '%' 
                }"
              ></view>
            </view>
            
            <!-- 数值标签 -->
            <view class="slider-labels">
              <text class="slider-label">16</text>
              <text class="slider-label">20</text>
              <text class="slider-label">30</text>
              <text class="slider-label">40</text>
              <text class="slider-label">50</text>
              <text class="slider-label">不限</text>
            </view>
            
            <!-- 双滑块 -->
            <view class="dual-slider">
              <slider
                :value="selectedAgeMin"
                :min="16"
                :max="60"
                @change="onMinAgeChange"
                activeColor="transparent"
                backgroundColor="transparent"
                block-color="var(--primary)"
                block-size="24"
                class="range-slider min-slider"
              />
              <slider
                :value="selectedAgeMax"
                :min="16"
                :max="60"
                @change="onMaxAgeChange"
                activeColor="transparent"
                backgroundColor="transparent"
                block-color="var(--primary)"
                block-size="24"
                class="range-slider max-slider"
              />
            </view>
          </view>
        </view>
      </view>

      <view class="popup-footer">
        <tui-button
          type="primary"
          width="100%"
          height="88rpx"
          @click="confirmSelection"
        >
          确定
        </tui-button>
      </view>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 性别选项
const genderOptions = [
  { value: 0, label: '不限' },
  { value: 1, label: '男' },
  { value: 2, label: '女' }
]

interface Props {
  show?: boolean
  gender?: number
  ageMin?: number
  ageMax?: number
}

interface Emits {
  (e: 'close'): void
  (e: 'confirm', data: { gender: number, ageMin: number, ageMax: number }): void
}

const props = withDefaults(defineProps<Props>(), {
  show: true,
  gender: 0,
  ageMin: 16,
  ageMax: 60
})

const emit = defineEmits<Emits>()

// 选中的值
const selectedGender = ref(props.gender)
const selectedAgeMin = ref(props.ageMin)
const selectedAgeMax = ref(props.ageMax)

// 计算滑块位置
const getSliderLeft = () => {
  return ((selectedAgeMin.value - 16) / (60 - 16)) * 100
}

const getSliderWidth = () => {
  const total = 60 - 16
  const range = selectedAgeMax.value - selectedAgeMin.value
  return (range / total) * 100
}

// 年龄变化处理
const onMinAgeChange = (e: any) => {
  const value = e.detail.value
  selectedAgeMin.value = value
  if (value > selectedAgeMax.value) {
    selectedAgeMax.value = value
  }
}

const onMaxAgeChange = (e: any) => {
  const value = e.detail.value
  selectedAgeMax.value = value
  if (value < selectedAgeMin.value) {
    selectedAgeMin.value = value
  }
}

// 确认选择
const confirmSelection = () => {
  emit('confirm', {
    gender: selectedGender.value,
    ageMin: selectedAgeMin.value,
    ageMax: selectedAgeMax.value
  })
}
</script>

<style lang="scss" scoped>
.popup-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  border-bottom: 1rpx solid var(--border-light);
  background: var(--bg-card);
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-base);
}

.popup-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--bg-search);
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
    background: var(--bg-tag);
  }
}

.close-icon {
  font-size: 40rpx;
  color: var(--text-secondary);
  line-height: 1;
}

.popup-content {
  flex: 1;
  background: var(--bg-card);
  overflow-y: auto;
}

.form-section {
  margin-bottom: var(--spacing-5);

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-base);
  margin-bottom: var(--spacing-3);
}

.popup-footer {
  padding: var(--spacing-4);
  background: var(--bg-card);
  border-top: 1rpx solid var(--border-light);
}

.form-item {
  margin-bottom: var(--spacing-6);
}

.form-label {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: var(--text-base);
  margin-bottom: var(--spacing-3);
}

.gender-section {
  padding: var(--spacing-4) var(--spacing-4) var(--spacing-6);
}

.gender-options {
  display: flex;
  gap: var(--spacing-3);
}

.gender-option {
  flex: 1;
  padding: var(--spacing-3);
  background: var(--bg-search);
  border-radius: var(--radius-2);
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2rpx solid var(--border-light);
  min-height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    transform: scale(0.95);
  }

  &.active {
    background: var(--primary);
    border-color: var(--primary);

    .option-text {
      color: var(--text-inverse);
      font-weight: 600;
    }
  }
}

.option-text {
  font-size: 32rpx;
  color: var(--text-base);
  font-weight: 500;
}

.age-display-section {
  padding: 0 var(--spacing-4) var(--spacing-6);
  text-align: center;
}

.age-display-text {
  font-size: 48rpx;
  font-weight: 600;
  color: var(--text-base);
}

.age-range {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.age-input {
  width: 120rpx;
  height: 80rpx;
  padding: 0 16rpx;
  background-color: var(--bg-input);
  border-radius: var(--radius-2);
  font-size: 28rpx;
  color: var(--text-base);
  text-align: center;
  border: 2rpx solid transparent;
  
  &:focus {
    border-color: var(--primary);
    background-color: var(--bg-card);
  }
}

.age-separator {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.age-unit {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.age-slider-section {
  padding: 0 var(--spacing-4);
}

.age-range-slider {
  position: relative;
  padding: var(--spacing-4) 0;
}

.slider-track {
  height: 8rpx;
  background: var(--bg-search);
  border-radius: 4rpx;
  position: relative;
  margin: var(--spacing-4) 0;
}

.slider-range {
  position: absolute;
  height: 100%;
  background: var(--primary);
  border-radius: 4rpx;
  transition: all 0.3s ease;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
}

.slider-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  text-align: center;
}

.dual-slider {
  position: relative;
  height: 60rpx;
}

.range-slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.min-slider {
  z-index: 2;
}

.max-slider {
  z-index: 1;
}
</style>