<template>
  <tui-bottom-popup :show="true" @close="$emit('close')" :height="600">
    <view class="popup-container">
      <view class="popup-header">
        <text class="popup-title">选择工种</text>
        <view class="popup-close" @click="$emit('close')">
          <text class="close-icon">×</text>
        </view>
      </view>

      <view class="popup-content">
        <view class="job-types-grid">
          <view 
            v-for="jobType in jobTypes" 
            :key="jobType.value"
            class="job-type-item"
            :class="{ active: selectedType === jobType.value }"
            @click="selectType(jobType.value)"
          >
            <text class="job-type-text">{{ jobType.label }}</text>
          </view>
        </view>
      </view>

      <view class="popup-footer">
        <tui-button
          type="primary"
          width="100%"
          height="88rpx"
          @click="confirmSelection"
        >
          确定
        </tui-button>
      </view>
    </view>
  </tui-bottom-popup>
</template>

<script setup lang="ts">
interface Props {
  jobType?: string
}

interface Emits {
  (e: 'close'): void
  (e: 'confirm', jobType: string): void
}

const props = withDefaults(defineProps<Props>(), {
  jobType: ''
})

const emit = defineEmits<Emits>()

// 工种选项
const jobTypes = [
  { label: '电子厂-普工', value: '电子厂-普工' },
  { label: '服装厂-普工', value: '服装厂-普工' },
  { label: '食品厂-普工', value: '食品厂-普工' },
  { label: '包装工', value: '包装工' },
  { label: '分拣员', value: '分拣员' },
  { label: '搬运工', value: '搬运工' },
  { label: '清洁工', value: '清洁工' },
  { label: '保安', value: '保安' },
  { label: '送餐员', value: '送餐员' },
  { label: '快递员', value: '快递员' },
  { label: '导购员', value: '导购员' },
  { label: '收银员', value: '收银员' },
  { label: '客服', value: '客服' },
  { label: '话务员', value: '话务员' },
  { label: '数据录入', value: '数据录入' },
  { label: '其他', value: '其他' }
]

// 选中的工种
const selectedType = ref(props.jobType || jobTypes[0].value)

// 选择工种
const selectType = (type: string) => {
  selectedType.value = type
}

// 确认选择
const confirmSelection = () => {
  emit('confirm', selectedType.value)
}
</script>

<style lang="scss" scoped>
.popup-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--bg-card);
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-bottom: 2rpx solid var(--border-light);
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-base);
}

.popup-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--bg-search);
}

.close-icon {
  font-size: 36rpx;
  color: var(--text-secondary);
  font-weight: 300;
}

.popup-content {
  flex: 1;
  padding: var(--spacing-4);
  overflow-y: auto;
  background: var(--bg-page);
}

.job-types-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-3);
}

.job-type-item {
  padding: var(--spacing-3) var(--spacing-2);
  background: var(--bg-search);
  border-radius: var(--radius-2);
  border: 2rpx solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  min-height: 88rpx;

  &:active {
    transform: scale(0.95);
  }

  &.active {
    background: var(--bg-primary-light);
    border-color: var(--primary);

    .job-type-text {
      color: var(--primary);
      font-weight: 600;
    }
  }
}

.job-type-text {
  font-size: 28rpx;
  color: var(--text-base);
  text-align: center;
  line-height: 1.2;
}

.popup-footer {
  padding: var(--spacing-4);
  border-top: 2rpx solid var(--border-light);
  background: var(--bg-card);
}
</style>