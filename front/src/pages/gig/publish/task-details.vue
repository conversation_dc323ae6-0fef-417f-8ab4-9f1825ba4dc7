<template>
  <view class="container">
    <!-- 表单内容 -->
    <view class="page-content">
      <!-- 招工标题 -->
      <view class="card">
        <view class="card-title">
          <text>招工标题</text>
        </view>
        <view class="title-input-container">
          <input
            v-model="formData.title"
            type="text"
            placeholder="电子厂普工"
            class="title-input"
            maxlength="20"
          />
        </view>
      </view>

      <!-- 详情描述 -->
      <view class="card">
        <view class="card-title">
          <text>详情描述（非必填）</text>
        </view>
        <view class="desc-container">
          <tui-textarea
            v-model="formData.description"
            placeholder="补充工作要求或详细内容，请勿填写与手机号或微信号"
            :maxlength="500"
            height="200rpx"
            :is-counter="true"
            :textarea-border="true"
            background-color="var(--bg-input)"
            :radius="16"
            :size="30"
            border-color="transparent"
            :border-top="false"
            :border-bottom="false"
            padding="24rpx"
          />
        </view>
      </view>

      <!-- 上传媒体 -->
      <view class="card">
        <view class="media-upload">
          <view class="upload-item" @click="uploadVideo">
            <view class="upload-icon-container">
              <text class="i-solar:videocamera-linear upload-icon"></text>
            </view>
            <text class="upload-text">上传视频</text>
          </view>
          <view class="upload-item" @click="uploadImage">
            <view class="upload-icon-container">
              <text class="i-solar:camera-linear upload-icon"></text>
            </view>
            <text class="upload-text">上传照片</text>
          </view>
        </view>

        <!-- 媒体预览 -->
        <view
          v-if="formData.images.length > 0 || formData.videos.length > 0"
          class="media-preview"
        >
          <!-- 图片预览 -->
          <view v-if="formData.images.length > 0" class="preview-section">
            <text class="preview-title"
              >已上传图片 ({{ formData.images.length }})</text
            >
            <view class="image-list">
              <view
                v-for="(image, index) in formData.images"
                :key="index"
                class="image-item"
                @click="previewImage(index)"
              >
                <image :src="image" class="preview-image" mode="aspectFill" />
                <view class="remove-btn" @click.stop="removeImage(index)">
                  <text class="i-solar:close-circle-linear remove-icon"></text>
                </view>
              </view>
            </view>
          </view>

          <!-- 视频预览 -->
          <view v-if="formData.videos.length > 0" class="preview-section">
            <text class="preview-title"
              >已上传视频 ({{ formData.videos.length }})</text
            >
            <view class="video-list">
              <view
                v-for="(video, index) in formData.videos"
                :key="index"
                class="video-item"
              >
                <video :src="video" class="preview-video" controls />
                <view class="remove-btn" @click="removeVideo(index)">
                  <text class="i-solar:close-circle-linear remove-icon"></text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view class="upload-tip">
          <text class="tip-text"
            >上传有工作内容的视频或照片，增加50%的接单率</text
          >
          <text class="tip-link" @click="viewExample('image')">图片示例</text>
          <text class="tip-link" @click="viewExample('video')">视频示例</text>
        </view>
      </view>

      <!-- 工作要求或福利 -->
      <view class="card">
        <view class="card-title">
          <text>工作要求或福利（可多选）</text>
        </view>
        <view class="tag-selector-wrapper">
          <TagSelector
            v-model="selectedTags"
            :options="gigTagOptions"
            multiple
            :max="5"
            @change="handleTagChange"
          />
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <BottomActionBar>
      <view class="next-btn" @click="confirmDetails">
        <text class="btn-text">确定</text>
      </view>
    </BottomActionBar>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import BottomActionBar from "@/components/common/BottomActionBar.vue";
import TagSelector from "@/components/common/TagSelector.vue";
import { GIG_REQUIREMENT_TAGS } from "@/constants/gig";

// 表单数据
const formData = ref({
  title: "",
  description: "",
  images: [] as string[],
  videos: [] as string[],
});

// 选中的标签值数组
const selectedTags = ref<string[]>([]);

// 转换零工标签数据为 TagOption 格式
const gigTagOptions = computed(() => {
  return GIG_REQUIREMENT_TAGS.map(tag => ({
    label: tag.label,
    value: tag.value,
    disabled: false
  }));
});

// 标签变化处理
const handleTagChange = (value: string[], option: any, index: number) => {
  console.log('标签选择变化:', value, '选中项:', option, '下标:', index);
};

const uploadVideo = () => {
  uni.chooseVideo({
    sourceType: ["camera", "album"],
    maxDuration: 60,
    success: (res) => {
      formData.value.videos.push(res.tempFilePath);
      uni.showToast({
        title: "视频上传成功",
        icon: "success",
      });
    },
  });
};

const uploadImage = () => {
  uni.chooseImage({
    count: 9 - formData.value.images.length,
    sizeType: ["original", "compressed"],
    sourceType: ["album", "camera"],
    success: (res) => {
      formData.value.images.push(...res.tempFilePaths);
      uni.showToast({
        title: "图片上传成功",
        icon: "success",
      });
    },
  });
};

const viewExample = (type: "image" | "video") => {
  uni.showToast({
    title: `查看${type === "image" ? "图片" : "视频"}示例`,
    icon: "none",
  });
};

const previewImage = (index: number) => {
  uni.previewImage({
    current: index,
    urls: formData.value.images,
  });
};

const removeImage = (index: number) => {
  formData.value.images.splice(index, 1);
  uni.showToast({
    title: "图片已删除",
    icon: "success",
  });
};

const removeVideo = (index: number) => {
  formData.value.videos.splice(index, 1);
  uni.showToast({
    title: "视频已删除",
    icon: "success",
  });
};

const confirmDetails = () => {
  if (!formData.value.title.trim()) {
    uni.showToast({
      title: "请填写招工标题",
      icon: "none",
    });
    return;
  }

  // 返回数据给上一页
  const pages = getCurrentPages();
  const prevPage = pages[pages.length - 2];
  if (prevPage) {
    prevPage.$vm.updateTaskDetails({
      ...formData.value,
      selectedTags: selectedTags.value,
    });
  }

  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: var(--bg-page);
  padding-bottom: 120rpx;
}

// 页面内容
.page-content {
  padding: 20rpx;
  padding-bottom: 120rpx; // 为底部固定按钮留出空间
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

// 标题输入
.title-input-container {
  position: relative;
  margin-top: -8rpx; // 减少card-title和输入框的间距
}

.title-input {
  height: 80rpx;
  padding: 0 24rpx;
  background: var(--bg-input);
  border-radius: var(--radius-2);
  font-size: 28rpx;
  border: none;
}

// 描述输入
.desc-container {
  margin-top: -8rpx; // 减少card-title和输入框的间距
}

// 媒体上传
.media-upload {
  display: flex;
  gap: 40rpx;
  margin-bottom: 24rpx;
  margin-top: -8rpx; // 减少与card顶部的间距
}

.upload-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 32rpx 24rpx;
  background: var(--bg-input);
  border-radius: var(--radius-2);
  transition: all 0.3s ease;
}

.upload-item:active {
  transform: scale(0.98);
  background: var(--bg-grey);
}

.upload-icon-container {
  width: 80rpx;
  height: 80rpx;
  background: var(--primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 36rpx;
  color: var(--text-inverse);
}

.upload-text {
  font-size: 26rpx;
  font-weight: 500;
}

.upload-tip {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-wrap: wrap;
}

.tip-text {
  font-size: 22rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

.tip-link {
  font-size: 22rpx;
  color: var(--primary);
  text-decoration: underline;
}

// 标签选择器包装
.tag-selector-wrapper {
  margin-top: -8rpx; // 减少card-title和标签的间距
}

// 媒体预览
.media-preview {
  margin: 24rpx 0;
}

.preview-section {
  margin-bottom: 24rpx;
}

.preview-title {
  font-size: 26rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
  display: block;
}

.image-list,
.video-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item,
.video-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: var(--radius-2);
  overflow: hidden;
}

.preview-image,
.preview-video {
  width: 100%;
  height: 100%;
  border-radius: var(--radius-2);
}

.remove-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-icon {
  font-size: 24rpx;
  color: var(--text-inverse);
}

// 使用BottomActionBar组件，单按钮布局覆盖
.next-btn {
  width: 100%;
  flex: none; /* 覆盖app.css中的flex: 2 */
}
</style>
