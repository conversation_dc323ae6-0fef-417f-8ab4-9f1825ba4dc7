<template>
  <BottomPopup
    :show="show"
    title="联系电话"
    @close="handleClose"
    @update:show="$emit('update:show', $event)"
  >
    <view class="contact-form">
      <!-- 联系人姓名 -->
      <view class="form-item">
        <view class="form-label">联系人</view>
        <input
          v-model="formData.contactName"
          class="form-input"
          placeholder="请输入联系人姓名"
          maxlength="10"
        />
      </view>

      <!-- 联系电话 -->
      <view class="form-item">
        <view class="form-label">联系电话</view>
        <input
          v-model="formData.contactPhone"
          class="form-input"
          placeholder="请输入联系电话"
          type="text"
          maxlength="11"
        />
      </view>

      <!-- 默认联系人提示 -->
      <view class="default-contact-hint">
        <view class="hint-icon">
          <text class="i-solar-info-circle-linear" />
        </view>
        <text class="hint-text">
          默认使用您的真实姓名和绑定手机号：{{ maskedDefaultInfo }}
        </text>
      </view>

      <!-- 操作按钮 -->
      <view class="form-actions">
        <view class="btn-secondary" @click="useDefaultInfo">
          <text class="btn-text">使用默认信息</text>
        </view>
        <view class="btn-primary" @click="handleConfirm">
          <text class="btn-text">确定</text>
        </view>
      </view>
    </view>
  </BottomPopup>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import BottomPopup from '@/components/common/BottomPopup.vue'
import { formatPhoneNumber } from '@/utils/core/string'
import { validatePhone } from '@/utils/core/validation'
import { showToast } from '@/utils/ui/feedback'
import { useUserStore } from '@/stores/user'

interface ContactInfo {
  contactName: string
  contactPhone: string
}

interface Props {
  show: boolean
  /** 当前联系人姓名 */
  contactName?: string
  /** 当前联系电话 */
  contactPhone?: string
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'confirm', data: ContactInfo): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  contactName: '',
  contactPhone: ''
})

const emit = defineEmits<Emits>()

const userStore = useUserStore()

// 表单数据
const formData = ref<ContactInfo>({
  contactName: '',
  contactPhone: ''
})

// 脱敏后的默认信息显示
const maskedDefaultInfo = computed(() => {
  const user = userStore.user
  if (!user) return '请先登录'
  
  const maskedName = user.real_name ? 
    user.real_name.length > 1 ? 
      user.real_name.charAt(0) + '*'.repeat(user.real_name.length - 1) 
      : user.real_name 
    : user.nickname
  const maskedPhone = formatPhoneNumber(user.phone)
  
  return `${maskedName} ${maskedPhone}`
})

// 简化props监听，使用Object.assign
watch(
  () => ({ name: props.contactName, phone: props.contactPhone }),
  ({ name, phone }) => Object.assign(formData.value, { contactName: name, contactPhone: phone }),
  { immediate: true }
)

// 简化默认信息填充
const useDefaultInfo = () => {
  const user = userStore.user
  if (!user) {
    showToast('请先登录')
    return
  }
  
  formData.value.contactName = user.real_name || user.nickname
  formData.value.contactPhone = user.phone
}

// 简化表单验证逻辑
const validateForm = (): boolean => {
  const { contactName, contactPhone } = formData.value
  const trimmedName = contactName.trim()
  const trimmedPhone = contactPhone.trim()
  
  const validations = [
    { condition: !trimmedName, message: '请输入联系人姓名' },
    { condition: trimmedName.length < 2, message: '联系人姓名至少2个字符' },
    { condition: !trimmedPhone, message: '请输入联系电话' },
    { condition: !validatePhone(trimmedPhone), message: '请输入正确的手机号码' }
  ]
  
  for (const { condition, message } of validations) {
    if (condition) {
      showToast(message)
      return false
    }
  }
  
  return true
}

// 简化确认处理
const handleConfirm = () => {
  if (!validateForm()) return
  
  const { contactName, contactPhone } = formData.value
  emit('confirm', {
    contactName: contactName.trim(),
    contactPhone: contactPhone.trim()
  })
}

// 关闭弹框
const handleClose = () => {
  emit('close')
}
</script>

<style lang="scss" scoped>
.contact-form {
  padding: 32rpx 24rpx;
}

.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}

.form-label {
  width: 140rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-base);
  flex-shrink: 0;
}

.form-input {
  flex: 1;
  height: 88rpx;
  padding: 0 24rpx;
  background: var(--bg-input);
  border-radius: var(--radius-2);
  font-size: 28rpx;
  color: var(--text-base);
  border: none;
  
  &::placeholder {
    color: var(--text-grey);
  }
}

.default-contact-hint {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
  background: var(--bg-search);
  border-radius: var(--radius-2);
  border-left: 6rpx solid var(--primary);
}

.hint-icon {
  color: var(--primary);
  font-size: 32rpx;
  flex-shrink: 0;
  margin-top: 2rpx;
}

.hint-text {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.5;
}

.form-actions {
  display: flex;
  gap: 24rpx;
  margin-top: 48rpx;
}

.btn-secondary {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-search);
  border-radius: var(--radius-2);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    opacity: 0.8;
  }
}

.btn-primary {
  flex: 2;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  border-radius: var(--radius-2);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 16rpx rgba(255, 165, 0, 0.3);
  }
}

.btn-text {
  font-size: 30rpx;
  font-weight: 500;
  color: var(--text-base);
  
  .btn-primary & {
    color: var(--text-white);
  }
}
</style>