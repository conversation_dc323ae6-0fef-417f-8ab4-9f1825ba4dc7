<template>
  <BottomPopup
    v-model:show="isVisible"
    title="零工接单设置"
    :show-close-btn="true"
    @close="handleClose"
    bg-color="var(--bg-page)"
  >
    <!-- 抢单设置 -->
    <view class="settings-section">
      <view class="section-title">报名方式</view>
      <view class="setting-item-container">
        <ListItem
          icon="i-solar-flash-bold"
          title="抢单模式"
          subtitle="开启后零工无需审核，直接录用"
          :show-arrow="false"
        >
          <template #right>
            <view class="switch-container">
              <switch
                :checked="settings.grabOrderEnabled"
                color="var(--primary)"
                @change="toggle('grabOrderEnabled')"
              />
            </view>
          </template>
        </ListItem>
      </view>
    </view>

    <!-- 零工开工时 -->
    <view class="settings-section">
      <view class="section-title">零工开工时</view>
      <view class="setting-item-container">
        <!-- 开工码 -->
        <ListItem
          icon="i-solar-qr-code-bold"
          title="开工码"
          subtitle="防止零工提前开工，敬请打卡"
          :show-arrow="false"
        >
          <template #right>
            <view class="switch-container">
              <switch
                :checked="settings.workCodeEnabled"
                color="var(--primary)"
                @change="toggle('workCodeEnabled')"
              />
            </view>
          </template>
        </ListItem>
      </view>
    </view>

    <!-- 零工早退时 -->
    <view class="settings-section">
      <view class="section-title">零工早退时</view>
      <view class="setting-item-container">
        <!-- 早退码 -->
        <ListItem
          icon="i-solar-logout-2-bold"
          title="早退码"
          subtitle="防止零工不打招呼就早退跑路"
          :show-arrow="false"
        >
          <template #right>
            <view class="switch-container">
              <switch
                :checked="settings.earlyLeaveEnabled"
                color="#FF8A00"
                @change="toggle('earlyLeaveEnabled')"
              />
            </view>
          </template>
        </ListItem>
      </view>
    </view>

    <!-- 确定按钮 -->
    <view class="confirm-btn" @click="confirmSettings">
      <text class="confirm-text">确定</text>
    </view>
  </BottomPopup>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive } from "vue";
import BottomPopup from "@/components/common/BottomPopup.vue";
import ListItem from "@/components/common/ListItem.vue";

interface Props {
  show: boolean;
  workCodeEnabled?: boolean;
  earlyLeaveEnabled?: boolean;
  /** 抢单模式：true-开启抢单(auto)，false-需要审核(manual) */
  grabOrderEnabled?: boolean;
}

interface Emits {
  (e: "update:show", value: boolean): void;
  (e: "confirm", settings: WorkerSettings): void;
}

interface WorkerSettings {
  workCodeEnabled: boolean;
  earlyLeaveEnabled: boolean;
  grabOrderEnabled: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  workCodeEnabled: false,
  earlyLeaveEnabled: false,
  grabOrderEnabled: false,
});

const emit = defineEmits<Emits>();

// 响应式数据
const isVisible = computed({
  get: () => props.show,
  set: (value) => emit("update:show", value),
});

// 简化状态管理，合并toggle方法
const settings = reactive({
  workCodeEnabled: props.workCodeEnabled,
  earlyLeaveEnabled: props.earlyLeaveEnabled,
  grabOrderEnabled: props.grabOrderEnabled
})

// 通用toggle方法，减少重复代码
const toggle = (key: keyof typeof settings) => {
  settings[key] = !settings[key]
}

const confirmSettings = () => {
  emit("confirm", { ...settings })
  isVisible.value = false
}

const handleClose = () => {
  isVisible.value = false;
};

// 简化props监听，合并为单个watch
watch(
  () => ({
    workCodeEnabled: props.workCodeEnabled,
    earlyLeaveEnabled: props.earlyLeaveEnabled,
    grabOrderEnabled: props.grabOrderEnabled
  }),
  (newSettings) => Object.assign(settings, newSettings),
  { immediate: true }
)
</script>

<style lang="scss" scoped>
// 设置区域
.settings-section {
  margin-bottom: 48rpx;
}

.section-title {
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-bottom: 24rpx;
  padding-left: 8rpx;
  font-weight: 500;
  position: relative;
}

.setting-item-container {
  margin-bottom: 16rpx;
}

.switch-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 确定按钮
.confirm-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #ffd700 0%, #ffa500 100%);
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 165, 0, 0.4);
  transition: all 0.3s ease;
  margin-top: 40rpx;
}

.confirm-btn:active {
  box-shadow: 0 4rpx 16rpx rgba(255, 165, 0, 0.6);
}

.confirm-text {
  font-size: 32rpx;
  color: var(--text-base);
  font-weight: 600;
}
</style>
