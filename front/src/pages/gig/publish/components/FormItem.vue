<template>
  <view class="form-item" @click="handleClick">
    <view class="item-label">
      <text class="label-text">{{ label }}</text>
    </view>

    <view class="item-content">
      <view class="item-value">
        <!-- 插槽内容 -->
        <slot>
          <text class="value-text" :class="{ placeholder: isPlaceholder }">
            {{ displayValue }}
          </text>
          <text v-if="subText" class="sub-text">{{ subText }}</text>
        </slot>
      </view>

      <!-- 右侧图标 -->
      <view v-if="rightIcon || showArrow" class="item-icon">
        <text class="i-solar:alt-arrow-right-linear arrow-icon"></text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  label: string;
  value?: string | number;
  placeholder?: string;
  subText?: string;
  showArrow?: boolean;
  rightIcon?: string;
  clickable?: boolean;
}

interface Emits {
  (e: "click"): void;
}

const props = withDefaults(defineProps<Props>(), {
  showArrow: true,
  clickable: true,
});

const emit = defineEmits<Emits>();

// 计算显示值
const displayValue = computed(() => {
  if (props.value !== undefined && props.value !== "") {
    return props.value;
  }
  return props.placeholder || "";
});

// 是否为占位符状态
const isPlaceholder = computed(() => {
  return !props.value || props.value === "";
});

// 点击处理
const handleClick = () => {
  if (props.clickable) {
    emit("click");
  }
};
</script>

<style lang="scss" scoped>
.form-item {
  background: var(--bg-card);
  border-radius: var(--radius-2);
  padding: var(--spacing-4) var(--spacing-3);
  margin-bottom: var(--spacing-3);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.item-label {
  display: flex;
  align-items: center;
}

.label-text {
  font-size: 32rpx;
  // font-weight: 500;
}

.item-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  flex: 1;
  justify-content: flex-end;
}

.item-value {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
}

.value-text {
  font-size: 28rpx;
  color: var(--text-base);
  text-align: right;

  &.placeholder {
    color: var(--text-grey);
  }
}

.sub-text {
  font-size: 24rpx;
  color: var(--text-grey);
  text-align: right;
}

.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-icon {
  font-size: 36rpx;
  color: var(--text-grey);
  display: inline-block;
}
</style>
