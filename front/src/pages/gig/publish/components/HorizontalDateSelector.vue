<template>
  <view class="horizontal-date-selector">
    <scroll-view
      scroll-x
      class="date-scroll"
      :show-scrollbar="false"
      :scroll-with-animation="true"
    >
      <view class="date-list">
        <view
          v-for="dateOption in dateOptions"
          :key="dateOption.date"
          class="date-option"
          :class="{
            active: selectedDate === dateOption.date,
            today: dateOption.isToday,
          }"
          @click="selectDate(dateOption.date)"
        >
          <view class="date-content">
            <view class="date-label-container">
              <text class="date-label-main">{{
                getMainLabel(dateOption.label)
              }}</text>
              <text class="date-label-sub">{{
                getSubLabel(dateOption.label)
              }}</text>
            </view>
            <text class="date-value">{{ dateOption.dateText }}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { formatDate, DateFormat } from "@/utils/core/date";

interface Props {
  selectedDate: string;
}

interface Emits {
  (e: "update:date", date: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 生成接下来7天的日期选项
const dateOptions = computed(() => {
  const options = [];
  const today = new Date();

  for (let i = 0; i < 7; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);

    const dateStr = formatDate(date, DateFormat.DATE_ONLY);
    const isToday = i === 0;

    // 生成标签
    let label = "";
    if (i === 0) {
      label = "今日";
    } else if (i === 1) {
      label = "明日";
    } else if (i === 2) {
      label = "后日";
    } else {
      const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      label = weekdays[date.getDay()];
    }

    // 生成日期文本
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const dateText = `${month}月${day}日`;

    options.push({
      date: dateStr,
      label,
      dateText,
      isToday,
    });
  }

  return options;
});

// 选择日期
const selectDate = (date: string) => {
  emit("update:date", date);
};

// 获取主标签（今、明、后）
const getMainLabel = (label: string) => {
  if (label === "今日") return "今";
  if (label === "明日") return "明";
  if (label === "后日") return "后";
  return "";
};

// 获取副标签（星期几）
const getSubLabel = (label: string) => {
  if (label === "今日" || label === "明日" || label === "后日") {
    const today = new Date();
    let targetDate = new Date(today);

    if (label === "明日") {
      targetDate.setDate(today.getDate() + 1);
    } else if (label === "后日") {
      targetDate.setDate(today.getDate() + 2);
    }

    const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    return weekdays[targetDate.getDay()];
  }
  return label;
};
</script>

<style lang="scss" scoped>
.horizontal-date-selector {
  width: 100%;
  padding: 16rpx 0;
}

.date-scroll {
  width: 100%;
}

.date-list {
  display: flex;
  gap: 20rpx;
  // padding: 0 32rpx;
  white-space: nowrap;
}

.date-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 20rpx;
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border: 2rpx solid var(--border-color);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  &.today {
    border-color: var(--primary);
    background: linear-gradient(135deg, #fff5f0 0%, #ffe8d6 100%);
    box-shadow: 0 4rpx 16rpx rgba(255, 109, 0, 0.15);

    &::before {
      opacity: 1;
    }

    .date-label-main {
      color: #ff6d00;
      font-weight: 500;
    }

    .date-label-sub {
      color: #ff8a00;
    }

    .date-value {
      color: #ff6d00;
      font-weight: 500;
    }
  }

  &.active {
    background: linear-gradient(135deg, #ff6d00 0%, #ff8a00 100%);
    border-color: #ff6d00;
    box-shadow: 0 8rpx 24rpx rgba(255, 109, 0, 0.3),
      0 4rpx 12rpx rgba(255, 109, 0, 0.2);

    .date-label-main,
    .date-label-sub,
    .date-value {
      color: #ffffff;
      text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
    }
  }
}

.date-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  position: relative;
  z-index: 1;
}

.date-label-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12rpx;
  justify-content: center;
}

.date-label-main {
  font-size: 32rpx;
}

.date-label-sub {
  font-size: 32rpx;
  color: var(--text-secondary);
}

.date-value {
  font-size: 28rpx;
  line-height: 1.4;
  color: var(--text-info);
}
</style>
