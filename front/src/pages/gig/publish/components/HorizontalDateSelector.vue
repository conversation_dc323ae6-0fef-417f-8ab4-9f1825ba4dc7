<template>
  <view class="horizontal-date-selector">
    <scroll-view
      scroll-x
      class="date-scroll"
      :show-scrollbar="false"
      :scroll-with-animation="true"
    >
      <view class="date-list">
        <view
          v-for="dateOption in dateOptions"
          :key="dateOption.date"
          class="date-option"
          :class="{
            active: selectedDate === dateOption.date,
            today: dateOption.isToday,
          }"
          @click="selectDate(dateOption.date)"
        >
          <view class="date-content">
            <text class="date-label">{{ dateOption.label }}</text>
            <text class="date-value">{{ dateOption.dateText }}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { formatDate, DateFormat } from "@/utils/core/date";

interface Props {
  selectedDate: string;
}

interface Emits {
  (e: "update:date", date: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 生成接下来7天的日期选项
const dateOptions = computed(() => {
  const options = [];
  const today = new Date();

  for (let i = 0; i < 7; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);

    const dateStr = formatDate(date, DateFormat.DATE_ONLY);
    const isToday = i === 0;

    // 生成标签
    let label = "";
    if (i === 0) {
      label = "今日";
    } else if (i === 1) {
      label = "明日";
    } else if (i === 2) {
      label = "后日";
    } else {
      const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      label = weekdays[date.getDay()];
    }

    // 生成日期文本
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const dateText = `${month}月${day}日`;

    options.push({
      date: dateStr,
      label,
      dateText,
      isToday,
    });
  }

  return options;
});

// 选择日期
const selectDate = (date: string) => {
  emit("update:date", date);
};
</script>

<style lang="scss" scoped>
.horizontal-date-selector {
  width: 100%;
  padding: 16rpx 0;
}

.date-scroll {
  width: 100%;
}

.date-list {
  display: flex;
  gap: 20rpx;
  padding: 0 32rpx;
  white-space: nowrap;
}

.date-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 130rpx;
  height: 120rpx;
  border-radius: var(--radius-3);
  background: var(--bg-search);
  border: 2rpx solid var(--border-light);
  transition: all 0.3s ease;
  cursor: pointer;
  flex-shrink: 0;

  &:active {
    transform: scale(0.95);
  }

  &.today {
    border-color: var(--primary);
    background: var(--bg-primary-light);
  }

  &.active {
    background: var(--primary);
    border-color: var(--primary);
    box-shadow: 0 4rpx 12rpx rgba(255, 109, 0, 0.3);

    .date-label,
    .date-value {
      color: var(--text-inverse);
    }
  }
}

.date-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.date-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  font-weight: 500;
  line-height: 1.2;
}

.date-value {
  font-size: 26rpx;
  color: var(--text-base);
  font-weight: 600;
  line-height: 1.2;
}

.date-option.active {
  .date-label {
    color: var(--text-inverse);
  }

  .date-value {
    color: var(--text-inverse);
  }
}
</style>
