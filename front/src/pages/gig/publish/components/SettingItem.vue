<template>
  <view class="setting-item" @click="handleClick">
    <!-- 左侧图标 -->
    <view class="setting-icon">
      <view :class="icon" class="icon"></view>
    </view>

    <!-- 中间内容 -->
    <view class="setting-content">
      <view class="setting-title">{{ title }}</view>
      <view class="setting-subtitle">{{ subtitle }}</view>
    </view>

    <!-- 右侧状态 -->
    <view class="setting-status">
      <view class="status-wrapper">
        <!-- 状态指示点 -->
        <view class="status-dot" :class="{ enabled: enabled }"></view>
        <!-- 状态文字 -->
        <text class="status-text" :class="{ enabled: enabled }">{{
          value
        }}</text>
      </view>
      <!-- 箭头 -->
      <view class="arrow">
        <view
          class="i-solar:alt-arrow-right-linear text-24rpx text-secondary"
        ></view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Props {
  icon: string;
  title: string;
  subtitle: string;
  value: string;
  enabled?: boolean;
}

interface Emits {
  (e: "click"): void;
}

const props = withDefaults(defineProps<Props>(), {
  enabled: false,
});

const emit = defineEmits<Emits>();

const handleClick = () => {
  emit("click");
};
</script>

<style lang="scss" scoped>
.setting-item {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  background: var(--bg-card);
  border-radius: var(--radius-3);
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
  border: 1rpx solid var(--border-light);
}

.setting-item:active {
  transform: scale(0.98);
  background: var(--bg-search);
}

// 左侧图标
.setting-icon {
  width: 80rpx;
  height: 80rpx;
  background: var(--bg-search);
  border-radius: var(--radius-2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.icon {
  font-size: 40rpx;
  color: var(--text-base);
}

// 中间内容
.setting-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.setting-title {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-base);
  line-height: 1.2;
}

.setting-subtitle {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.3;
}

// 右侧状态
.setting-status {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-shrink: 0;
}

.status-wrapper {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: var(--border-base);
  transition: all 0.3s ease;
}

.status-dot.enabled {
  background: var(--text-green);
  box-shadow: 0 0 8rpx rgba(34, 197, 94, 0.3);
}

.status-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.status-text.enabled {
  color: var(--text-green);
  font-weight: 500;
}

.arrow {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
