<template>
  <BottomPopup
    v-model:show="isVisible"
    title="选择每日工作时间"
    :show-close-btn="true"
    @close="handleClose"
  >
    <!-- 时间显示区域 -->
    <view class="time-display">
      <view class="time-item">
        <text class="time-label">开工时间</text>
        <text class="time-value">{{ formatTime(startTime) }}</text>
      </view>
      <view class="time-item">
        <text class="time-label">收工时间</text>
        <text class="time-value">{{ formatEndTime(endTime) }}</text>
      </view>
    </view>

    <!-- 工作时长显示 -->
    <view class="duration-display">
      <text class="duration-text">{{ workDuration }}</text>
    </view>

    <!-- 时间选择器 -->
    <view class="time-picker-container">
      <view class="picker-section">
        <text class="picker-title">开工时间</text>
        <view class="picker-wrapper">
          <picker-view
            class="time-picker"
            :value="startPickerValue"
            @change="onStartTimeChange"
          >
            <picker-view-column>
              <view v-for="hour in hours" :key="hour" class="picker-item">
                {{ formatNumber(hour) }}
              </view>
            </picker-view-column>
            <picker-view-column>
              <view v-for="minute in minutes" :key="minute" class="picker-item">
                {{ formatNumber(minute) }}
              </view>
            </picker-view-column>
          </picker-view>
        </view>
      </view>

      <view class="picker-divider">
        <text class="divider-text">至</text>
      </view>

      <view class="picker-section">
        <text class="picker-title">收工时间</text>
        <view class="picker-wrapper">
          <picker-view
            class="time-picker"
            :value="endPickerValue"
            @change="onEndTimeChange"
          >
            <picker-view-column>
              <view v-for="hour in hours" :key="hour" class="picker-item">
                {{ formatNumber(hour) }}
              </view>
            </picker-view-column>
            <picker-view-column>
              <view v-for="minute in minutes" :key="minute" class="picker-item">
                {{ formatNumber(minute) }}
              </view>
            </picker-view-column>
          </picker-view>
        </view>
      </view>
    </view>

    <!-- 添加休息时段按钮 -->
    <view class="add-break-btn" @click="addBreakTime">
      <text class="i-solar:add-circle-linear add-icon"></text>
      <text class="add-text">添加休息时段</text>
    </view>

    <!-- 确定按钮 -->
    <view class="confirm-btn" @click="confirmSelection">
      <text class="confirm-text">确定</text>
    </view>
  </BottomPopup>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import BottomPopup from "@/components/common/BottomPopup.vue";

interface Props {
  show: boolean;
  startTime?: { hour: number; minute: number };
  endTime?: { hour: number; minute: number };
}

interface Emits {
  (e: "update:show", value: boolean): void;
  (
    e: "confirm",
    data: {
      startTime: { hour: number; minute: number };
      endTime: { hour: number; minute: number };
      duration: string;
    }
  ): void;
}

const props = withDefaults(defineProps<Props>(), {
  startTime: () => ({ hour: 18, minute: 50 }),
  endTime: () => ({ hour: 2, minute: 50 }),
});

const emit = defineEmits<Emits>();

// 响应式数据
const isVisible = computed({
  get: () => props.show,
  set: (value) => emit("update:show", value),
});

const startTime = ref({ ...props.startTime });
const endTime = ref({ ...props.endTime });

// 生成小时和分钟数组
const hours = Array.from({ length: 24 }, (_, i) => i);
const minutes = Array.from({ length: 6 }, (_, i) => i * 10); // 10分钟间隔：0, 10, 20, 30, 40, 50

// picker-view的值 - 需要找到对应的索引
const startPickerValue = ref([
  hours.findIndex((h) => h === startTime.value.hour),
  minutes.findIndex((m) => m === startTime.value.minute),
]);
const endPickerValue = ref([
  hours.findIndex((h) => h === endTime.value.hour),
  minutes.findIndex((m) => m === endTime.value.minute),
]);

// 计算工作时长
const workDuration = computed(() => {
  const start = startTime.value.hour * 60 + startTime.value.minute;
  let end = endTime.value.hour * 60 + endTime.value.minute;

  // 处理跨日情况
  if (end <= start) {
    end += 24 * 60; // 加一天
  }

  const duration = end - start;
  const hours = Math.floor(duration / 60);
  const mins = duration % 60;

  if (mins === 0) {
    return `共${hours}小时`;
  } else {
    return `共${hours}小时${mins}分钟`;
  }
});

// 格式化时间显示
const formatTime = (time: { hour: number; minute: number }) => {
  return `${formatNumber(time.hour)}:${formatNumber(time.minute)}`;
};

const formatEndTime = (time: { hour: number; minute: number }) => {
  const start = startTime.value.hour * 60 + startTime.value.minute;
  const end = time.hour * 60 + time.minute;

  if (end <= start) {
    return `次日 ${formatTime(time)}`;
  }
  return formatTime(time);
};

const formatNumber = (num: number) => {
  return num.toString().padStart(2, "0");
};

// 事件处理
const onStartTimeChange = (e: any) => {
  const [hourIndex, minuteIndex] = e.detail.value;
  startTime.value = {
    hour: hours[hourIndex],
    minute: minutes[minuteIndex],
  };
  startPickerValue.value = [hourIndex, minuteIndex];
};

const onEndTimeChange = (e: any) => {
  const [hourIndex, minuteIndex] = e.detail.value;
  endTime.value = {
    hour: hours[hourIndex],
    minute: minutes[minuteIndex],
  };
  endPickerValue.value = [hourIndex, minuteIndex];
};

const addBreakTime = () => {
  uni.showToast({
    title: "添加休息时段功能开发中",
    icon: "none",
  });
};

const confirmSelection = () => {
  emit("confirm", {
    startTime: startTime.value,
    endTime: endTime.value,
    duration: workDuration.value,
  });
  isVisible.value = false;
};

const handleClose = () => {
  isVisible.value = false;
};

// 监听props变化
watch(
  () => props.startTime,
  (newVal) => {
    if (newVal) {
      startTime.value = { ...newVal };
      startPickerValue.value = [
        hours.findIndex((h) => h === newVal.hour),
        minutes.findIndex((m) => m === newVal.minute),
      ];
    }
  },
  { deep: true }
);

watch(
  () => props.endTime,
  (newVal) => {
    if (newVal) {
      endTime.value = { ...newVal };
      endPickerValue.value = [
        hours.findIndex((h) => h === newVal.hour),
        minutes.findIndex((m) => m === newVal.minute),
      ];
    }
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
// 时间显示区域
.time-display {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 32rpx 0;
  margin-bottom: 24rpx;
}

.time-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.time-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.time-value {
  font-size: 36rpx;
  color: var(--text-base);
  font-weight: 600;
}

// 工作时长显示
.duration-display {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 0;
  margin-bottom: 32rpx;
}

.duration-text {
  font-size: 28rpx;
  color: var(--primary);
  font-weight: 600;
  background: var(--bg-search);
  padding: 12rpx 24rpx;
  border-radius: var(--radius-2);
}

// 时间选择器容器
.time-picker-container {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 40rpx;
}

.picker-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.picker-title {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
  text-align: center;
}

.picker-wrapper {
  width: 100%;
  height: 300rpx;
  background: var(--bg-search);
  border-radius: var(--radius-2);
  overflow: hidden;
}

.time-picker {
  width: 100%;
  height: 100%;
}

.picker-item {
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: var(--text-base);
  font-weight: 500;
}

.picker-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 300rpx;
  margin-top: 40rpx; /* 与picker-title的高度对齐 */
}

.divider-text {
  font-size: 32rpx;
  color: var(--text-base);
  font-weight: 600;
}

// 添加休息时段按钮
.add-break-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx;
  background: var(--bg-search);
  border-radius: var(--radius-2);
  margin-bottom: 40rpx;
  transition: all 0.3s ease;
}

.add-break-btn:active {
  transform: scale(0.98);
  background: var(--bg-grey);
}

.add-icon {
  font-size: 32rpx;
  color: var(--primary);
}

.add-text {
  font-size: 28rpx;
  color: var(--primary);
  font-weight: 500;
}

// 确定按钮
.confirm-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #ffd700 0%, #ffa500 100%);
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 165, 0, 0.4);
  transition: all 0.3s ease;
}

.confirm-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(255, 165, 0, 0.6);
}

.confirm-text {
  font-size: 32rpx;
  color: var(--text-base);
  font-weight: 600;
}
</style>
