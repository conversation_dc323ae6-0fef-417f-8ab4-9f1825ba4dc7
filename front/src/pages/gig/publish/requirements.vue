<template>
  <view class="container">
    <view class="form-content">
      <Card title="需求人数" sub-title="首单免费发布（限制5人及以下）">
        <view class="content-space">
          <Stepper
            v-model="peopleCount"
            unit="人"
            :min="1"
            :max="5"
            :step="1"
            :precision="0"
          />
        </view>
      </Card>

      <!-- 工价设置 -->
      <Card title="工价设置">
        <template #right>
          <view class="segmented-control">
            <view
              v-for="item in priceTypes"
              :key="item.value"
              class="segmented-control-item"
              :class="{ active: priceType === item.value }"
              @click="setPriceType(item.value)"
            >
              <text class="control-text">{{ item.label }}</text>
            </view>
            <view
              class="segmented-control-indicator"
              :style="{ transform: `translateX(${indicatorPosition}%)` }"
            />
          </view>
        </template>
        <view class="content-space">
          <Stepper
            v-model="salary"
            :unit="priceType === 'hourly' ? '元/小时' : '元/件'"
            :min="1"
            :max="999"
            :business-mode="priceType"
          />
        </view>
      </Card>

      <!-- 零工接单设置 -->
      <ListItem
        title="零工接单设置"
        :value="workerSettingsText"
        subtitle="抢名、开工、收工设置"
        show-arrow
        @click="showWorkerSettings"
      />

      <!-- 联系电话 -->
      <ListItem
        title="联系电话"
        :value="contactDisplayText"
        subtitle="可修改添加联系人"
        show-arrow
        @click="showContactForm"
      />
    </view>

    <!-- 底部按钮组 -->
    <BottomActionBar>
      <view class="action-buttons">
        <view class="prev-btn" @click="prevStep">
          <text class="btn-text">上一步</text>
        </view>
        <view class="publish-btn yellow" @click="publishGig">
          <text class="btn-text">发布招工</text>
        </view>
      </view>
    </BottomActionBar>

    <!-- 零工接单设置弹框 -->
    <WorkerSettingsModal
      :show="showWorkerSettingsModal"
      :work-code-enabled="workerSettings.workCodeEnabled"
      :early-leave-enabled="workerSettings.earlyLeaveEnabled"
      :grab-order-enabled="workerSettings.grabOrderEnabled"
      @confirm="onWorkerSettingsConfirm"
      @update:show="showWorkerSettingsModal = $event"
    />

    <!-- 联系电话设置弹框 -->
    <ContactFormModal
      :show="showContactFormModal"
      :contact-name="contactInfo.name"
      :contact-phone="contactInfo.phone"
      @confirm="onContactFormConfirm"
      @close="showContactFormModal = false"
      @update:show="showContactFormModal = $event"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import Card from "@/components/common/Card.vue";
import ListItem from "@/components/common/ListItem.vue";
import Stepper from "@/components/common/Stepper.vue";
import BottomActionBar from "@/components/common/BottomActionBar.vue";
import WorkerSettingsModal from "./components/WorkerSettingsModal.vue";
import ContactFormModal from "./components/ContactFormModal.vue";
import { formatPhoneNumber } from "@/utils/core/string";
import { useUserStore } from "@/stores/user";

type PriceType = "hourly" | "piece";

interface PriceTypeOption {
  label: string;
  value: PriceType;
}

const userStore = useUserStore();

const peopleCount = ref<number | null>(1);
const salary = ref<number | null>(null);
const priceType = ref<PriceType>("hourly");

// 联系电话相关状态
const showContactFormModal = ref(false);
const contactInfo = ref({
  name: "",
  phone: "",
});

const priceTypes: PriceTypeOption[] = [
  { label: "计时", value: "hourly" },
  { label: "计件", value: "piece" },
];

const indicatorPosition = computed(() => {
  return priceType.value === "hourly" ? 0 : 100;
});

const setPriceType = (type: PriceType) => {
  priceType.value = type;
};

// 零工接单设置相关
const showWorkerSettingsModal = ref(false);
const workerSettings = ref({
  workCodeEnabled: false,
  earlyLeaveEnabled: false,
  grabOrderEnabled: false, // 抢单功能
});

// 合并用户信息访问，简化联系信息显示
const { userInfo, contactDisplayText } = (() => {
  const user = computed(() => userStore.user);
  const displayText = computed(() => {
    const { name, phone } = contactInfo.value;
    if (name && phone) {
      return `${name} ${formatPhoneNumber(phone)}`;
    }

    const currentUser = user.value;
    if (currentUser) {
      const displayName = currentUser.real_name || currentUser.nickname;
      return `${displayName} ${formatPhoneNumber(currentUser.phone)}`;
    }

    return "点击设置联系人";
  });

  return { userInfo: user, contactDisplayText: displayText };
})();

// 简化设置文字生成
const workerSettingsText = computed(() => {
  const settingsMap = {
    grabOrderEnabled: "抢单模式",
    workCodeEnabled: "开工码",
    earlyLeaveEnabled: "早退码",
  };

  const enabled = Object.entries(workerSettings.value)
    .filter(([, value]) => value)
    .map(([key]) => settingsMap[key as keyof typeof settingsMap]);

  return enabled.length > 0 ? `已开启: ${enabled.join("、")}` : "";
});

// 显示零工接单设置弹框
const showWorkerSettings = () => {
  showWorkerSettingsModal.value = true;
};

const onWorkerSettingsConfirm = (settings: typeof workerSettings.value) => {
  Object.assign(workerSettings.value, settings);
  console.log("零工接单设置已更新:", settings);
};

// 简化联系信息初始化
const showContactForm = () => {
  const user = userInfo.value;
  if (!contactInfo.value.name && user) {
    Object.assign(contactInfo.value, {
      name: user.real_name || user.nickname,
      phone: user.phone,
    });
  }
  showContactFormModal.value = true;
};

// 简化联系信息更新
const onContactFormConfirm = ({
  contactName,
  contactPhone,
}: {
  contactName: string;
  contactPhone: string;
}) => {
  Object.assign(contactInfo.value, { name: contactName, phone: contactPhone });
  showContactFormModal.value = false;
  console.log("联系电话已更新:", { contactName, contactPhone });
};

const selectWorkers = () => {
  console.log("选择指定零工");
};

const prevStep = () => {
  console.log("上一步");
};

const publishGig = () => {
  const gigData = {
    peopleCount: peopleCount.value,
    salary: salary.value,
    priceType: priceType.value,
    contactInfo: { ...contactInfo.value },
    workerSettings: {
      ...workerSettings.value,
      approvalMode: workerSettings.value.grabOrderEnabled ? "auto" : "manual",
    },
  };

  console.log("发布零工", gigData);

  // TODO: 调用API发布零工
  // await GigApi.create(gigData)
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh; /* 确保容器占满整个屏幕高度 */
  overflow-y: auto; /* 内容超出时可滚动 */
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-top: 16rpx;
}

.form-section .form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 分段控制器
.segmented-control {
  position: relative;
  display: flex;
  background-color: var(--bg-search);
  border-radius: 12rpx;
  padding: 6rpx;
  width: 240rpx;
}

.segmented-control-item {
  flex: 1;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.control-text {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-secondary);
  .segmented-control-item.active & {
    color: var(--primary);
    font-weight: 600;
  }
}

.segmented-control-indicator {
  position: absolute;
  top: 6rpx;
  left: 6rpx;
  width: calc(50% - 6rpx);
  height: 64rpx;
  background-color: var(--bg-card);
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

/* 底部按钮组 - 使用BottomActionBar组件 */
.action-buttons {
  display: flex;
  gap: 24rpx;
}
</style>
