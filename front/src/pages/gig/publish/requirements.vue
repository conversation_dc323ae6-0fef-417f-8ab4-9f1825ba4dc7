<template>
  <view class="container">
    <view class="scroll-content">
      <!-- 表单内容 -->
      <view class="form-content">
        <!-- 需求人数 -->
        <view class="form-section">
          <view class="form-header">
            <view class="form-item-left">
              <text class="form-item-title">需求人数</text>
              <text class="form-item-desc">首单免费发布（限制5人及以下）</text>
            </view>
          </view>
          <view class="form-content">
            <Stepper
              v-model="peopleCount"
              unit="人"
              :min="1"
              :max="5"
              :step="1"
              :precision="0"
            />
          </view>
        </view>

        <!-- 工价设置 -->
        <view class="form-section">
          <view class="form-header">
            <view class="form-item-left">
              <text class="form-item-title">工价设置</text>
            </view>
            <view class="segmented-control">
              <view
                v-for="item in priceTypes"
                :key="item.value"
                class="segmented-control-item"
                :class="{ active: priceType === item.value }"
                @click="setPriceType(item.value)"
              >
                <text class="control-text">{{ item.label }}</text>
              </view>
              <view
                class="segmented-control-indicator"
                :style="{ transform: `translateX(${indicatorPosition}%)` }"
              />
            </view>
          </view>
          <view class="form-content">
            <Stepper
              v-model="salary"
              :unit="priceType === 'hourly' ? '元/小时' : '元/件'"
              :min="0"
              :max="999"
              :step="1"
              :precision="1"
            />
          </view>
        </view>

        <!-- 邀请指定零工接单 -->
        <FormItem
          label="邀请指定零工接单"
          placeholder="请选择"
          :show-arrow="true"
          @click="selectWorkers"
        />

        <!-- 零工接单设置 -->
        <FormItem
          label="零工接单设置"
          sub-text="抢名、开工、收工设置"
          @click="orderSettings"
        />

        <!-- 联系电话 -->
        <FormItem
          label="联系电话"
          value="13126606919"
          sub-text="可修改添加联系人"
          @click="editContact"
        />
      </view>
    </view>

    <!-- 底部按钮组 -->
    <BottomActionBar>
      <view class="action-buttons">
        <view class="prev-btn" @click="prevStep">
          <text class="btn-text">上一步</text>
        </view>
        <view class="publish-btn yellow" @click="publishGig">
          <text class="btn-text">发布招工</text>
        </view>
      </view>
    </BottomActionBar>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import FormItem from "./components/FormItem.vue";
import Stepper from "@/components/common/Stepper.vue";
import BottomActionBar from "@/components/common/BottomActionBar.vue";

type PriceType = "hourly" | "piece";

interface PriceTypeOption {
  label: string;
  value: PriceType;
}

const peopleCount = ref<number | null>(1);
const salary = ref<number | null>(null);
const priceType = ref<PriceType>("hourly");

const priceTypes: PriceTypeOption[] = [
  { label: "计时", value: "hourly" },
  { label: "计件", value: "piece" },
];

const indicatorPosition = computed(() => {
  return priceType.value === "hourly" ? 0 : 100;
});

const setPriceType = (type: PriceType) => {
  priceType.value = type;
};

const selectWorkers = () => {
  console.log("选择指定零工");
};

const orderSettings = () => {
  console.log("零工接单设置");
};

const editContact = () => {
  console.log("编辑联系方式");
};

const prevStep = () => {
  console.log("上一步");
};

const publishGig = () => {
  console.log("发布零工", {
    peopleCount: peopleCount.value,
    salary: salary.value,
    priceType: priceType.value,
  });
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh; /* 确保容器占满整个屏幕高度 */
}

.scroll-content {
  flex: 1;
  overflow-y: auto; /* 内容超出时可滚动 */
  padding: 20rpx;
  padding-bottom: 120rpx; /* 为底部固定按钮留出空间 */
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.form-section .form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 分段控制器
.segmented-control {
  position: relative;
  display: flex;
  background-color: var(--bg-search);
  border-radius: 12rpx;
  padding: 6rpx;
  width: 240rpx;
}

.segmented-control-item {
  flex: 1;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.control-text {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-secondary);
  .segmented-control-item.active & {
    color: var(--primary);
    font-weight: 600;
  }
}

.segmented-control-indicator {
  position: absolute;
  top: 6rpx;
  left: 6rpx;
  width: calc(50% - 6rpx);
  height: 64rpx;
  background-color: var(--bg-card);
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

/* 底部按钮组 - 使用BottomActionBar组件 */
.action-buttons {
  display: flex;
  gap: 24rpx;
}
</style>
