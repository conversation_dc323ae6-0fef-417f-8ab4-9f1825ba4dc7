<template>
  <view class="container">
    <!-- 表单内容 -->
    <view class="form-content">
      <!-- 任务详情 -->
      <FormItem
        label="任务详情"
        :value="taskDetail"
        placeholder="请填写任务详情"
        @click="editTaskDetail"
      />

      <!-- 性别年龄 -->
      <FormItem
        label="性别年龄"
        :value="genderAgeText"
        placeholder="性别不限、16岁~不限"
        @click="selectGenderAge"
      />

      <!-- 工作日期 -->
      <view class="form-section">
        <view class="form-header">
          <text class="form-item-title">工作日期</text>
        </view>
        <view class="form-content date-selector-content">
          <HorizontalDateSelector
            :selected-date="selectedDate"
            @update:date="onDateSelect"
          />
        </view>
      </view>

      <!-- 工作时间 -->
      <view class="form-section">
        <view class="form-header">
          <text class="form-item-title">工作时间</text>
          <text class="form-item-action" @click="selectWorkTime">修改</text>
        </view>
        <view class="work-time-display" v-if="workTimeText" @click="selectWorkTime">
          <view class="work-time-item">
            <text class="work-time-label">开工时间</text>
            <text class="work-time-value">{{ formatTime(workStartTime) }}</text>
          </view>
          <view class="work-duration">
            <text class="duration-label">每日工作时长</text>
            <text class="duration-value">{{ workTimeText }}</text>
          </view>
          <view class="work-time-item">
            <text class="work-time-label">收工时间</text>
            <text class="work-time-value">{{ formatEndTime(workEndTime, workStartTime) }}</text>
          </view>
        </view>
        <view class="work-time-placeholder" v-else @click="selectWorkTime">
          <text class="placeholder-text">请选择</text>
        </view>
      </view>

      <!-- 干活地点 -->
      <FormItem
        label="干活地点"
        :value="locationText"
        placeholder="设置干活地点"
        @click="selectLocation"
      />
    </view>

    <!-- 底部按钮 -->
    <BottomActionBar>
      <view class="next-btn" @click="nextStep">
        <text class="btn-text">下一步</text>
      </view>
    </BottomActionBar>

    <!-- 性别年龄选择器 -->
    <GenderAgeSelector
      :show="showGenderAgeSelector"
      :gender="selectedGender"
      :min-age="selectedMinAge"
      :max-age="selectedMaxAge"
      @confirm="onGenderAgeConfirm"
      @close="onGenderAgeClose"
    />

    <!-- 工作时间选择器 -->
    <WorkTimeSelector
      :show="showWorkTimeSelector"
      :start-time="workStartTime"
      :end-time="workEndTime"
      @confirm="onWorkTimeConfirm"
      @update:show="showWorkTimeSelector = $event"
    />
  </view>
</template>

<script setup lang="ts">
import FormItem from "./components/FormItem.vue";
import HorizontalDateSelector from "./components/HorizontalDateSelector.vue";
import GenderAgeSelector from "@/components/gig/GenderAgeSelector.vue";
import WorkTimeSelector from "./components/WorkTimeSelector.vue";
import BottomActionBar from "@/components/common/BottomActionBar.vue";
import { formatDate, DateFormat } from "@/utils/core/date";
import { genderOptions } from "@/constants/standards";

interface Emits {
  (e: "next"): void;
}

const emit = defineEmits<Emits>();

// 表单数据
const taskDetail = ref("");
const selectedDate = ref(formatDate(new Date(), DateFormat.DATE_ONLY));
const workTimeText = ref("");
const locationText = ref("");

// 性别年龄相关数据
const showGenderAgeSelector = ref(false);
const selectedGender = ref(0); // 0: 不限, 1: 男, 2: 女
const selectedMinAge = ref(16);
const selectedMaxAge = ref(65);

// 工作时间相关数据
const showWorkTimeSelector = ref(false);
const workStartTime = ref({ hour: 18, minute: 50 });
const workEndTime = ref({ hour: 2, minute: 50 });

// 计算性别年龄显示文本
const genderAgeText = computed(() => {
  const genderOption = genderOptions.find(
    (option) => option.code === selectedGender.value
  );
  const genderText = genderOption?.label || "不限";

  let ageText = "";
  if (selectedMinAge.value === 16 && selectedMaxAge.value === 65) {
    ageText = "16岁~不限";
  } else {
    const minText =
      selectedMinAge.value === 16 ? "16" : selectedMinAge.value.toString();
    const maxText =
      selectedMaxAge.value === 65 ? "不限" : selectedMaxAge.value.toString();
    ageText = `${minText}岁~${maxText === "不限" ? "不限" : maxText + "岁"}`;
  }

  return `${genderText}，${ageText}`;
});

// 任务详情数据
const taskDetailsData = ref({
  title: "",
  description: "",
  images: [] as string[],
  videos: [] as string[],
  selectedTags: [] as string[],
});

const editTaskDetail = () => {
  uni.navigateTo({
    url: "/pages/gig/publish/task-details",
  });
};

// 接收任务详情页面返回的数据
const updateTaskDetails = (data: any) => {
  taskDetailsData.value = data;
  taskDetail.value = data.title || "请填写任务详情";
  console.log("任务详情更新:", data);
};

const selectGenderAge = () => {
  showGenderAgeSelector.value = true;
};

const onGenderAgeConfirm = (gender: number, minAge: number, maxAge: number) => {
  selectedGender.value = gender;
  selectedMinAge.value = minAge;
  selectedMaxAge.value = maxAge;
  showGenderAgeSelector.value = false;
  console.log("性别年龄选择:", { gender, minAge, maxAge });
};

const onGenderAgeClose = () => {
  showGenderAgeSelector.value = false;
};

const onDateSelect = (date: string) => {
  selectedDate.value = date;
  console.log("选择日期:", date);
};

const selectWorkTime = () => {
  showWorkTimeSelector.value = true;
};

const onWorkTimeConfirm = (data: {
  startTime: { hour: number; minute: number };
  endTime: { hour: number; minute: number };
  duration: string;
}) => {
  workStartTime.value = data.startTime;
  workEndTime.value = data.endTime;
  workTimeText.value = data.duration;
  console.log("工作时间确认:", data);
};

// 格式化时间显示
const formatTime = (time: { hour: number; minute: number }) => {
  const formatNumber = (num: number) => num.toString().padStart(2, "0");
  return `${formatNumber(time.hour)}:${formatNumber(time.minute)}`;
};

const formatEndTime = (endTime: { hour: number; minute: number }, startTime: { hour: number; minute: number }) => {
  const start = startTime.hour * 60 + startTime.minute;
  const end = endTime.hour * 60 + endTime.minute;

  if (end <= start) {
    return `次日 ${formatTime(endTime)}`;
  }
  return formatTime(endTime);
};

const selectLocation = () => {
  uni.navigateTo({
    url: "/pages/gig/address-selection/index",
  });
};

// 接收地址选择页面返回的数据
const updateLocation = (address: any) => {
  locationText.value = address.title;
  console.log("地址更新:", address);
};

const nextStep = () => {
  emit("next");
};

// 暴露方法给外部调用
defineExpose({
  updateTaskDetails,
  updateLocation,
});
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh; /* 确保容器占满整个屏幕高度 */
}

.scroll-content {
  flex: 1;
  overflow-y: auto; /* 内容超出时可滚动 */
  padding: 20rpx;
  padding-bottom: 120rpx; /* 为底部固定按钮留出空间 */
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.form-section .form-header {
  padding-bottom: var(--spacing-3);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-item-title {
  font-size: 32rpx;
  color: var(--text-base);
}

/* 日期选择器特殊样式 - 移除左右padding使其占满 */
.date-selector-content {
  padding: 0 !important;
}

/* 使用BottomActionBar组件，单按钮布局覆盖 */
.next-btn {
  width: 100%;
  flex: none; /* 覆盖app.css中的flex: 2 */
}

/* 工作时间显示样式 */
.work-time-display {
  background: var(--bg-white);
  border-radius: var(--radius-2);
  padding: 32rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 2rpx solid var(--border-light);
  transition: all 0.3s ease;
}

.work-time-display:active {
  background: var(--bg-search);
  transform: scale(0.98);
}

.work-time-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.work-time-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.work-time-value {
  font-size: 36rpx;
  color: var(--text-base);
  font-weight: 600;
}

.work-duration {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.duration-label {
  font-size: 22rpx;
  color: var(--text-secondary);
}

.duration-value {
  font-size: 28rpx;
  color: var(--primary);
  font-weight: 600;
  background: var(--bg-search);
  padding: 8rpx 16rpx;
  border-radius: var(--radius-1);
}

.work-time-placeholder {
  background: var(--bg-white);
  border-radius: var(--radius-2);
  padding: 32rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid var(--border-light);
  transition: all 0.3s ease;
}

.work-time-placeholder:active {
  background: var(--bg-search);
  transform: scale(0.98);
}

.placeholder-text {
  font-size: 28rpx;
  color: var(--text-placeholder);
}

.form-item-action {
  font-size: 28rpx;
  color: var(--primary);
  font-weight: 500;
}
</style>
