<template>
  <view class="container">
    <!-- 表单内容 -->
    <view class="content">
      <!-- 任务详情 -->
      <ListItem
        margin="0"
        title="任务详情"
        :value="taskDetail"
        placeholder="请填写任务详情"
        show-arrow
        @click="editTaskDetail"
      />

      <!-- 性别年龄 -->
      <ListItem
        margin="0"
        title="性别年龄"
        :value="genderAgeText"
        placeholder="性别不限、16岁~不限"
        show-arrow
        @click="selectGenderAge"
      />

      <!-- 工作日期 -->
      <Card title="工作日期" :padding="cardPadding">
        <view class="date-selector-content">
          <HorizontalDateSelector
            :selected-date="selectedDate"
            @update:date="onDateSelect"
          />
        </view>
      </Card>

      <!-- 工作时间 -->
      <Card :padding="cardPadding">
        <template #header>
          <ListItem
            padding="0"
            margin="0"
            title="工作时间"
            :placeholder="workTimeText !== '' ? '修改' : '请选择'"
            show-arrow
            @click="selectWorkTime"
          />
        </template>

        <!-- 已选择时间状态 -->
        <view
          class="work-time-content content-space"
          v-if="workTimeText !== ''"
          @click="selectWorkTime"
        >
          <view class="time-row">
            <view class="time-info">
              <text class="time-label">开工时间</text>
              <text class="time-value">{{ formatTime(workStartTime) }}</text>
            </view>
            <view class="duration-info">
              <text class="duration-sub">工作时长</text>
              <text class="duration-label">{{ workTimeText }}</text>
            </view>
            <view class="time-info">
              <text class="time-label">收工时间</text>
              <view class="time-value-container">
                <view class="time-value">{{
                  formatEndTime(workEndTime, workStartTime).time
                }}</view>
                <view
                  v-if="formatEndTime(workEndTime, workStartTime).isNextDay"
                  class="next-day-tag"
                >
                  <text class="next-day-text">次日</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </Card>

      <!-- 干活地点 -->
      <Card :padding="cardPadding">
        <template #header>
          <ListItem
            padding="0"
            margin="0"
            title="干活地点"
            :placeholder="workTimeText !== '' ? '修改' : '请选择'"
            show-arrow
            @click="selectLocation"
          />
        </template>
        <view class="location-section">
          <!-- 地址内容 -->
          <view v-if="selectedAddress" class="location-content">
            <view class="location-info">
              <view class="location-icon">
                <view
                  class="i-solar:map-point-bold text-32rpx text-primary"
                ></view>
              </view>
              <view class="location-details">
                <text class="location-name">{{ selectedAddress.name }}</text>
                <text class="location-address">{{
                  selectedAddress.fullAddress
                }}</text>
              </view>
            </view>
          </view>
        </view>
      </Card>
    </view>

    <!-- 底部按钮 -->
    <BottomActionBar>
      <view class="next-btn" @click="nextStep">
        <text class="btn-text">下一步</text>
      </view>
    </BottomActionBar>

    <!-- 性别年龄选择器 -->
    <GenderAgeSelector
      v-model:show="showGenderAgeSelector"
      :gender="selectedGender"
      :min-age="selectedMinAge"
      :max-age="selectedMaxAge"
      @confirm="onGenderAgeConfirm"
    />

    <!-- 工作时间选择器 -->
    <WorkTimeSelector
      :show="showWorkTimeSelector"
      :start-time="workStartTime"
      :end-time="workEndTime"
      @confirm="onWorkTimeConfirm"
      @update:show="showWorkTimeSelector = $event"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import ListItem from "@/components/common/ListItem.vue";
import HorizontalDateSelector from "./components/HorizontalDateSelector.vue";
import GenderAgeSelector from "@/components/gig/GenderAgeSelector.vue";
import WorkTimeSelector from "./components/WorkTimeSelector.vue";
import BottomActionBar from "@/components/common/BottomActionBar.vue";
import Card from "@/components/common/Card.vue";
import { formatDate, DateFormat } from "@/utils/core/date";
import { genderOptions } from "@/constants/standards";

interface Emits {
  (e: "next"): void;
}

const emit = defineEmits<Emits>();
const cardPadding = "32rpx 24rpx";

// 表单数据
const taskDetail = ref("");
const selectedDate = ref(formatDate(new Date(), DateFormat.DATE_ONLY));
const workTimeText = ref("");
const locationText = ref("");

// 选中的地址信息
const selectedAddress = ref<{
  id: number;
  name: string;
  fullAddress: string;
  detailAddress: string;
  latitude: number;
  longitude: number;
  isDefault: boolean;
} | null>(null);

// 性别年龄相关数据
const showGenderAgeSelector = ref(false);
const selectedGender = ref(0); // 0: 不限, 1: 男, 2: 女
const selectedMinAge = ref(16);
const selectedMaxAge = ref(65);

// 工作时间相关数据
const showWorkTimeSelector = ref(false);
const workStartTime = ref({ hour: 18, minute: 50 });
const workEndTime = ref({ hour: 2, minute: 50 });

// 计算性别年龄显示文本
const genderAgeText = computed(() => {
  const genderOption = genderOptions.find(
    (option) => option.code === selectedGender.value
  );
  const genderText = genderOption?.label || "不限";

  let ageText = "";
  if (selectedMinAge.value === 16 && selectedMaxAge.value === 65) {
    ageText = "16岁~不限";
  } else {
    const minText =
      selectedMinAge.value === 16 ? "16" : selectedMinAge.value.toString();
    const maxText =
      selectedMaxAge.value === 65 ? "不限" : selectedMaxAge.value.toString();
    ageText = `${minText}岁~${maxText === "不限" ? "不限" : maxText + "岁"}`;
  }

  return `${genderText}，${ageText}`;
});

// 任务详情数据
const taskDetailsData = ref({
  title: "",
  description: "",
  images: [] as string[],
  videos: [] as string[],
  selectedTags: [] as string[],
});

const editTaskDetail = () => {
  uni.navigateTo({
    url: "/pages/gig/publish/task-details",
  });
};

// 接收任务详情页面返回的数据
const updateTaskDetails = (data: any) => {
  taskDetailsData.value = data;
  taskDetail.value = data.title || "请填写任务详情";
  console.log("任务详情更新:", data);
};

const selectGenderAge = () => {
  showGenderAgeSelector.value = true;
};

const onGenderAgeConfirm = (gender: number, minAge: number, maxAge: number) => {
  selectedGender.value = gender;
  selectedMinAge.value = minAge;
  selectedMaxAge.value = maxAge;
  showGenderAgeSelector.value = false;
  console.log("性别年龄选择:", { gender, minAge, maxAge });
};

const onDateSelect = (date: string) => {
  selectedDate.value = date;
  console.log("选择日期:", date);
};

const selectWorkTime = () => {
  showWorkTimeSelector.value = true;
};

const onWorkTimeConfirm = (data: {
  startTime: { hour: number; minute: number };
  endTime: { hour: number; minute: number };
  duration: string;
}) => {
  workStartTime.value = data.startTime;
  workEndTime.value = data.endTime;
  workTimeText.value = data.duration;
  console.log("工作时间确认:", data);
};

// 格式化时间显示
const formatTime = (time: { hour: number; minute: number }) => {
  const formatNumber = (num: number) => num.toString().padStart(2, "0");
  return `${formatNumber(time.hour)}:${formatNumber(time.minute)}`;
};

const formatEndTime = (
  endTime: { hour: number; minute: number },
  startTime: { hour: number; minute: number }
) => {
  const start = startTime.hour * 60 + startTime.minute;
  const end = endTime.hour * 60 + endTime.minute;

  return {
    time: formatTime(endTime),
    isNextDay: end <= start,
  };
};

const selectLocation = () => {
  uni.navigateTo({
    url: "/pages/gig/address-selection/index",
  });
};

// 接收地址选择页面返回的数据
const updateLocation = (address: any) => {
  selectedAddress.value = address;
  locationText.value = address.name;
  console.log("地址更新:", address);
};

const nextStep = () => {
  emit("next");
};

// 监听地址选择事件
const handleAddressSelected = (address: any) => {
  updateLocation(address);
};

// 页面生命周期
onMounted(() => {
  // 监听地址选择事件
  uni.$on("addressSelected", handleAddressSelected);
});

onUnmounted(() => {
  // 移除事件监听
  uni.$off("addressSelected", handleAddressSelected);
});

// 暴露方法给外部调用
defineExpose({
  updateTaskDetails,
  updateLocation,
});
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh; /* 确保容器占满整个屏幕高度 */
}

.scroll-content {
  flex: 1;
  overflow-y: auto; /* 内容超出时可滚动 */
  padding: 20rpx;
  padding-bottom: 120rpx; /* 为底部固定按钮留出空间 */
}

.content {
  margin: 0;
  gap: 24rpx;
}

/* Card组件内的表单项样式 */
.form-item-action {
  font-size: 28rpx;
  color: var(--primary);
  font-weight: 500;
}

/* 日期选择器特殊样式 - 移除左右padding使其占满 */
.date-selector-content {
  padding: 0 !important;
}

/* 使用BottomActionBar组件，单按钮布局覆盖 */
.next-btn {
  width: 100%;
  flex: none; /* 覆盖app.css中的flex: 2 */
}

.time-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background-color: var(--bg-page);
  border-radius: var(--radius-2);
}

.time-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  flex: 1;
  background-color: white;
  padding: 16rpx 0;
  border-radius: var(--radius-2);
}

.time-label {
  font-size: 26rpx;
  color: var(--text-info);
}

.time-value-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6rpx;
}

.time-value {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-base);
}

.next-day-tag {
  background: linear-gradient(135deg, #ff6d00 0%, #ff8a00 100%);
  padding: 3rpx 6rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 109, 0, 0.2);
  display: flex;
  justify-items: center;
  flex-direction: center;
}

.next-day-text {
  font-size: 24rpx;
  color: #ffffff;
}

.duration-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  flex: 1;
  padding: 16rpx 20rpx;
  margin: 0 16rpx;
}

.duration-sub {
  font-size: 26rpx;
  color: var(--text-info);
}

.duration-label {
  font-size: 28rpx;
  color: var(--primary);
  font-weight: 500;
}

// 地址选择区域样式
.location-section {
  width: 100%;
}

.location-info {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.location-icon {
  width: 48rpx;
  height: 48rpx;
  background: var(--bg-primary-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.location-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.location-name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
  line-height: 1.3;
}

.location-address {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.4;
  word-break: break-all;
}
</style>
